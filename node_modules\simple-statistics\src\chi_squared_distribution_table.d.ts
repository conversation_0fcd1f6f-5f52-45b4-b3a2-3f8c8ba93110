export interface DistributionTable {
    0.995: number;
    0.99: number;
    0.975: number;
    0.95: number;
    0.9: number;
    0.5: number;
    0.1: number;
    0.05: number;
    0.025: number;
    0.01: number;
    0.005: number;
}

/**
 * https://simple-statistics.github.io/docs/#chisquareddistributiontable
 */
declare const chiSquaredDistributionTable: {
    1: DistributionTable;
    2: DistributionTable;
    3: DistributionTable;
    4: DistributionTable;
    5: DistributionTable;
    6: DistributionTable;
    7: DistributionTable;
    8: DistributionTable;
    9: DistributionTable;
    10: DistributionTable;
    11: DistributionTable;
    12: DistributionTable;
    13: DistributionTable;
    14: DistributionTable;
    15: DistributionTable;
    16: DistributionTable;
    17: DistributionTable;
    18: DistributionTable;
    19: DistributionTable;
    20: DistributionTable;
    21: DistributionTable;
    22: DistributionTable;
    23: DistributionTable;
    24: DistributionTable;
    25: DistributionTable;
    26: DistributionTable;
    27: DistributionTable;
    28: DistributionTable;
    29: DistributionTable;
    30: DistributionTable;
    40: DistributionTable;
    50: DistributionTable;
    60: DistributionTable;
    70: DistributionTable;
    80: DistributionTable;
    90: DistributionTable;
    100: DistributionTable;
};

export default chiSquaredDistributionTable;
