/**
 * تطبيق الواجهة الأمامية المتقدم لنظام التداول الذكي
 * Advanced Frontend Application for Smart Trading System
 */

class AdvancedTradingDashboard {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.selectedInstrument = 'EURUSD';
        this.chart = null;
        this.chartData = [];
        
        // بيانات التداول
        this.tradingPairs = new Map();
        this.openTrades = new Map();
        this.closedTrades = [];
        this.accountData = {
            balance: 0,
            totalTrades: 0,
            winRate: 0,
            dailyPnL: 0,
            aiAccuracy: 0,
            openTrades: 0
        };
        
        // إعدادات التداول الآلي
        this.autoTradingSettings = {
            enabled: false,
            amount: 10,
            maxDailyTrades: 20,
            maxDailyLoss: 100,
            aiConfidenceThreshold: 0.8,
            enabledStrategies: {
                technical: true,
                ai: true
            }
        };
        
        // قائمة الأزواج الـ70
        this.targetPairs = [
            'GBPUSD', 'GBPUSD_otc', 'USDJPY', 'USDJPY_otc', 'CHFJPY', 'CHFJPY_otc',
            'USDCAD', 'USDCAD_otc', 'AUDCAD', 'AUDCAD_otc', 'USDCHF', 'USDCHF_otc',
            'EURGBP', 'EURGBP_otc', 'EURAUD', 'EURCAD', 'AUDUSD', 'AUDUSD_otc',
            'CADCHF', 'CADCHF_otc', 'EURJPY', 'EURJPY_otc', 'AUDCHF', 'GBPCHF',
            'AUDJPY', 'AUDJPY_otc', 'GBPJPY', 'GBPJPY_otc', 'GBPAUD', 'GBPAUD_otc',
            'GBPCAD', 'CADJPY', 'CADJPY_otc', 'EURCHF', 'EURCHF_otc', 'EURUSD', 'EURUSD_otc',
            'USDPHP_otc', 'USDSGD_otc', 'USDVND_otc', 'USDMYR_otc', 'NGNUSD_otc',
            'USDRUB_otc', 'TNDUSD_otc', 'NZDJPY_otc', 'USDTHB_otc', 'LBPUSD_otc',
            'USDBRL_otc', 'USDPKR_otc', 'EURNZD_otc', 'USDDZD_otc', 'USDEGP_otc',
            'NZDUSD_otc', 'AUDNZD_otc', 'YERUSD_otc', 'EURHUF_otc', 'USDMXN_otc',
            'IRRUSD_otc', 'USDBDT_otc', 'EURTRY_otc', 'USDIDR_otc', 'USDINR_otc',
            'USDCLP_otc', 'USDCNH_otc', 'USDCOP_otc', 'ZARUSD_otc', 'USDARS_otc',
            'EURRUB_otc', 'CHFNOK_otc'
        ];
        
        this.init();
    }

    init() {
        this.connectSocket();
        this.setupEventListeners();
        this.initializeChart();
        this.loadSettings();
        this.initializePairsTable();
        this.updateUI();
        this.startPeriodicUpdates();
    }

    /**
     * الاتصال بـ Socket.IO
     */
    connectSocket() {
        this.socket = io();

        this.socket.on('connect', () => {
            console.log('✅ Connected to server');
            this.updateConnectionStatus(true);
            this.addLogEntry('متصل بالخادم', 'success');
        });

        this.socket.on('disconnect', () => {
            console.log('❌ Disconnected from server');
            this.updateConnectionStatus(false);
            this.addLogEntry('انقطع الاتصال بالخادم', 'error');
        });

        // استقبال بيانات الأزواج
        this.socket.on('instrumentsUpdated', (data) => {
            this.updateTradingPairs(data);
        });

        // استقبال تحديثات الأسعار
        this.socket.on('priceUpdate', (data) => {
            this.updatePrice(data);
        });

        // استقبال الصفقات المفتوحة
        this.socket.on('openTradesUpdate', (trades) => {
            this.updateOpenTrades(trades);
        });

        // استقبال الصفقات المغلقة
        this.socket.on('closedTradesUpdate', (trades) => {
            this.updateClosedTrades(trades);
        });

        // استقبال تحديثات الحساب
        this.socket.on('accountUpdate', (data) => {
            this.updateAccountData(data);
        });

        // استقبال إشارات الذكاء الاصطناعي
        this.socket.on('aiSignal', (signal) => {
            this.displayAISignal(signal);
        });

        // استقبال نتائج الصفقات
        this.socket.on('tradeResult', (result) => {
            this.handleTradeResult(result);
        });
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // تبديل التداول الآلي
        document.getElementById('autoTradingSwitch').addEventListener('change', (e) => {
            this.toggleAutoTrading(e.target.checked);
        });

        // تحديث إعدادات التداول الآلي
        document.getElementById('autoTradeAmount').addEventListener('change', (e) => {
            this.autoTradingSettings.amount = parseFloat(e.target.value);
            this.saveSettings();
        });

        document.getElementById('maxDailyTrades').addEventListener('change', (e) => {
            this.autoTradingSettings.maxDailyTrades = parseInt(e.target.value);
            this.saveSettings();
        });

        document.getElementById('maxDailyLoss').addEventListener('change', (e) => {
            this.autoTradingSettings.maxDailyLoss = parseFloat(e.target.value);
            this.saveSettings();
        });

        // تحديث عتبة الثقة
        document.getElementById('aiConfidenceThreshold').addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            this.autoTradingSettings.aiConfidenceThreshold = value;
            document.getElementById('aiConfidenceValue').textContent = Math.round(value * 100) + '%';
            this.saveSettings();
        });

        // تحديث مستوى الثقة في الشريط الجانبي
        document.getElementById('confidenceLevel').addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            document.getElementById('confidenceValue').textContent = Math.round(value * 100) + '%';
        });
    }

    /**
     * تهيئة الرسم البياني
     */
    initializeChart() {
        const ctx = document.getElementById('priceChart').getContext('2d');
        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'السعر',
                    data: [],
                    borderColor: '#00d4ff',
                    backgroundColor: 'rgba(0, 212, 255, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: {
                            color: '#fff'
                        }
                    }
                },
                scales: {
                    x: {
                        ticks: {
                            color: '#fff'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    y: {
                        ticks: {
                            color: '#fff'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
    }

    /**
     * تهيئة جدول الأزواج
     */
    initializePairsTable() {
        const tbody = document.getElementById('pairsTableBody');
        tbody.innerHTML = '';

        this.targetPairs.forEach(pair => {
            const row = document.createElement('tr');
            row.id = `pair-${pair}`;
            row.innerHTML = `
                <td><strong>${pair}</strong></td>
                <td><span class="badge bg-secondary" id="status-${pair}">غير متوفر</span></td>
                <td><span id="profit-${pair}">-</span></td>
                <td><span id="price-${pair}">-</span></td>
                <td><span id="change-${pair}">-</span></td>
                <td><span id="ai-signal-${pair}" class="badge bg-secondary">-</span></td>
                <td><span id="ai-confidence-${pair}">-</span></td>
                <td>
                    <button class="btn btn-success btn-sm me-1" onclick="manualTrade('${pair}', 'call')" title="شراء">
                        <i class="fas fa-arrow-up"></i>
                    </button>
                    <button class="btn btn-danger btn-sm" onclick="manualTrade('${pair}', 'put')" title="بيع">
                        <i class="fas fa-arrow-down"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    /**
     * تحديث بيانات الأزواج
     */
    updateTradingPairs(data) {
        if (Array.isArray(data)) {
            data.forEach(pair => {
                this.tradingPairs.set(pair.symbol, pair);
                this.updatePairInTable(pair);
            });
        }
    }

    /**
     * تحديث زوج في الجدول
     */
    updatePairInTable(pair) {
        const symbol = pair.symbol || pair.id;
        
        // تحديث الحالة
        const statusElement = document.getElementById(`status-${symbol}`);
        if (statusElement) {
            if (pair.status === 'active' || pair.isActive) {
                statusElement.textContent = 'متاح';
                statusElement.className = 'badge bg-success';
            } else {
                statusElement.textContent = 'غير متاح';
                statusElement.className = 'badge bg-danger';
            }
        }

        // تحديث نسبة الربح
        const profitElement = document.getElementById(`profit-${symbol}`);
        if (profitElement && pair.payout) {
            profitElement.textContent = Math.round(pair.payout * 100) + '%';
        }

        // تحديث السعر
        const priceElement = document.getElementById(`price-${symbol}`);
        if (priceElement && pair.price) {
            priceElement.textContent = pair.price.toFixed(5);
        }

        // تحديث التغيير 24 ساعة
        const changeElement = document.getElementById(`change-${symbol}`);
        if (changeElement && pair.change24) {
            const change = pair.change24;
            changeElement.textContent = (change > 0 ? '+' : '') + (change * 100).toFixed(2) + '%';
            changeElement.className = change > 0 ? 'text-success' : 'text-danger';
        }
    }

    /**
     * تحديث السعر
     */
    updatePrice(data) {
        const symbol = data.symbol || data.assetId;
        const price = data.price || data.close;

        if (symbol && price) {
            const priceElement = document.getElementById(`price-${symbol}`);
            if (priceElement) {
                priceElement.textContent = price.toFixed(5);
            }

            // تحديث الرسم البياني إذا كان الزوج المحدد
            if (symbol === this.selectedInstrument) {
                this.updateChart(data);
                document.getElementById('currentPrice').textContent = price.toFixed(5);
            }
        }
    }

    /**
     * تحديث الرسم البياني
     */
    updateChart(data) {
        if (!this.chart) return;

        const time = new Date(data.timestamp || Date.now()).toLocaleTimeString();
        const price = data.price || data.close;

        this.chart.data.labels.push(time);
        this.chart.data.datasets[0].data.push(price);

        // الاحتفاظ بآخر 50 نقطة فقط
        if (this.chart.data.labels.length > 50) {
            this.chart.data.labels.shift();
            this.chart.data.datasets[0].data.shift();
        }

        this.chart.update('none');
    }

    /**
     * تحديث الصفقات المفتوحة
     */
    updateOpenTrades(trades) {
        this.openTrades.clear();
        const tbody = document.getElementById('openTradesTable');
        tbody.innerHTML = '';

        if (Array.isArray(trades)) {
            trades.forEach(trade => {
                this.openTrades.set(trade.id, trade);

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${trade.asset}</td>
                    <td><span class="badge ${trade.direction === 'call' ? 'bg-success' : 'bg-danger'}">${trade.direction.toUpperCase()}</span></td>
                    <td>$${trade.amount}</td>
                    <td><span id="countdown-${trade.id}">${this.formatTimeRemaining(trade.expirationTime)}</span></td>
                    <td>$${(trade.amount * (trade.payout || 0.8)).toFixed(2)}</td>
                `;
                tbody.appendChild(row);
            });
        }

        // تحديث عداد الصفقات المفتوحة
        this.accountData.openTrades = trades.length;
        document.getElementById('openTrades').textContent = trades.length;
    }

    /**
     * تحديث الصفقات المغلقة
     */
    updateClosedTrades(trades) {
        if (Array.isArray(trades)) {
            this.closedTrades = trades.slice(-10); // آخر 10 صفقات

            const tbody = document.getElementById('closedTradesTable');
            tbody.innerHTML = '';

            this.closedTrades.forEach(trade => {
                const row = document.createElement('tr');
                const isWin = trade.result === 'win';
                const pnl = isWin ? trade.amount * (trade.payout || 0.8) : -trade.amount;

                row.innerHTML = `
                    <td>${trade.asset}</td>
                    <td><span class="badge ${trade.direction === 'call' ? 'bg-success' : 'bg-danger'}">${trade.direction.toUpperCase()}</span></td>
                    <td>$${trade.amount}</td>
                    <td><span class="badge ${isWin ? 'bg-success' : 'bg-danger'}">${isWin ? 'ربح' : 'خسارة'}</span></td>
                    <td class="${pnl > 0 ? 'text-success' : 'text-danger'}">${pnl > 0 ? '+' : ''}$${pnl.toFixed(2)}</td>
                    <td>${new Date(trade.closeTime).toLocaleTimeString()}</td>
                `;
                tbody.appendChild(row);
            });
        }
    }

    /**
     * تحديث بيانات الحساب
     */
    updateAccountData(data) {
        Object.assign(this.accountData, data);

        document.getElementById('accountBalance').textContent = '$' + (data.balance || 0).toFixed(2);
        document.getElementById('totalTrades').textContent = data.totalTrades || 0;
        document.getElementById('winRate').textContent = Math.round((data.winRate || 0) * 100) + '%';
        document.getElementById('dailyPnL').textContent = '$' + (data.dailyPnL || 0).toFixed(2);
        document.getElementById('aiAccuracy').textContent = Math.round((data.aiAccuracy || 0) * 100) + '%';
    }

    /**
     * عرض إشارة الذكاء الاصطناعي
     */
    displayAISignal(signal) {
        const symbol = signal.asset;

        // تحديث في الجدول
        const signalElement = document.getElementById(`ai-signal-${symbol}`);
        const confidenceElement = document.getElementById(`ai-confidence-${symbol}`);

        if (signalElement && confidenceElement) {
            if (signal.direction === 'up') {
                signalElement.textContent = 'شراء';
                signalElement.className = 'badge bg-success';
            } else if (signal.direction === 'down') {
                signalElement.textContent = 'بيع';
                signalElement.className = 'badge bg-danger';
            } else {
                signalElement.textContent = 'محايد';
                signalElement.className = 'badge bg-secondary';
            }

            confidenceElement.textContent = Math.round(signal.confidence * 100) + '%';
        }

        // عرض في لوحة الإشارات
        this.addSignalToPanel(signal);

        // تسجيل في السجل
        this.addLogEntry(`إشارة ذكاء اصطناعي: ${signal.asset} - ${signal.direction} (${Math.round(signal.confidence * 100)}%)`, 'info');
    }

    /**
     * إضافة إشارة للوحة الإشارات
     */
    addSignalToPanel(signal) {
        const container = document.getElementById('signalsContainer');

        const signalDiv = document.createElement('div');
        signalDiv.className = `signal-card ${signal.direction === 'down' ? 'put' : ''}`;
        signalDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${signal.asset}</strong>
                    <span class="badge ${signal.direction === 'up' ? 'bg-success' : 'bg-danger'} ms-2">
                        ${signal.direction === 'up' ? 'شراء' : 'بيع'}
                    </span>
                </div>
                <div>
                    <small>الثقة: ${Math.round(signal.confidence * 100)}%</small>
                </div>
            </div>
            <small class="text-muted">${new Date().toLocaleTimeString()}</small>
        `;

        container.insertBefore(signalDiv, container.firstChild);

        // الاحتفاظ بآخر 5 إشارات فقط
        while (container.children.length > 5) {
            container.removeChild(container.lastChild);
        }
    }

    /**
     * تبديل التداول الآلي
     */
    toggleAutoTrading(enabled) {
        this.autoTradingSettings.enabled = enabled;
        document.getElementById('autoTradingLabel').textContent = enabled ? 'مفعل' : 'معطل';

        // إرسال للخادم
        this.socket.emit('toggleAutoTrading', {
            enabled: enabled,
            settings: this.autoTradingSettings
        });

        this.addLogEntry(`التداول الآلي ${enabled ? 'مفعل' : 'معطل'}`, enabled ? 'success' : 'warning');
        this.saveSettings();
    }

    /**
     * تنفيذ صفقة يدوية
     */
    manualTrade(asset, direction) {
        const amount = parseFloat(document.getElementById('tradeAmount').value);
        const duration = parseInt(document.getElementById('tradeDuration').value);

        if (!amount || amount <= 0) {
            alert('يرجى إدخال مبلغ صحيح');
            return;
        }

        const tradeData = {
            asset: asset,
            amount: amount,
            direction: direction,
            duration: duration,
            isManual: true
        };

        this.socket.emit('executeTrade', tradeData);
        this.addLogEntry(`تنفيذ صفقة يدوية: ${asset} - ${direction} - $${amount}`, 'info');
    }

    /**
     * معالجة نتيجة الصفقة
     */
    handleTradeResult(result) {
        const isWin = result.result === 'win';
        const message = `نتيجة الصفقة: ${result.asset} - ${isWin ? 'ربح' : 'خسارة'} - $${result.pnl}`;

        this.addLogEntry(message, isWin ? 'success' : 'error');

        // تحديث الإحصائيات
        this.updateAccountData(result.accountUpdate || {});
    }

    /**
     * تحديث حالة الاتصال
     */
    updateConnectionStatus(connected) {
        this.isConnected = connected;
        const statusElement = document.getElementById('connectionStatus');
        const indicatorElement = document.getElementById('connectionIndicator');

        if (connected) {
            statusElement.textContent = 'متصل';
            indicatorElement.className = 'status-indicator status-connected';
        } else {
            statusElement.textContent = 'غير متصل';
            indicatorElement.className = 'status-indicator status-disconnected';
        }
    }

    /**
     * إضافة مدخل للسجل
     */
    addLogEntry(message, type = 'info') {
        const logContainer = document.getElementById('activityLog');
        const entry = document.createElement('div');
        entry.className = 'log-entry';

        const timestamp = new Date().toLocaleTimeString();
        const icon = this.getLogIcon(type);
        const color = this.getLogColor(type);

        entry.innerHTML = `
            <span style="color: ${color};">${icon} ${message}</span>
            <span class="log-timestamp">${timestamp}</span>
        `;

        logContainer.insertBefore(entry, logContainer.firstChild);

        // الاحتفاظ بآخر 100 مدخل
        while (logContainer.children.length > 100) {
            logContainer.removeChild(logContainer.lastChild);
        }
    }

    /**
     * الحصول على أيقونة السجل
     */
    getLogIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        return icons[type] || 'ℹ️';
    }

    /**
     * الحصول على لون السجل
     */
    getLogColor(type) {
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };
        return colors[type] || '#17a2b8';
    }

    /**
     * تنسيق الوقت المتبقي
     */
    formatTimeRemaining(expirationTime) {
        const now = new Date().getTime();
        const expiry = new Date(expirationTime).getTime();
        const remaining = Math.max(0, expiry - now);

        const minutes = Math.floor(remaining / 60000);
        const seconds = Math.floor((remaining % 60000) / 1000);

        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    /**
     * حفظ الإعدادات
     */
    saveSettings() {
        localStorage.setItem('autoTradingSettings', JSON.stringify(this.autoTradingSettings));
    }

    /**
     * تحميل الإعدادات
     */
    loadSettings() {
        const saved = localStorage.getItem('autoTradingSettings');
        if (saved) {
            this.autoTradingSettings = { ...this.autoTradingSettings, ...JSON.parse(saved) };

            // تطبيق الإعدادات على الواجهة
            document.getElementById('autoTradeAmount').value = this.autoTradingSettings.amount;
            document.getElementById('maxDailyTrades').value = this.autoTradingSettings.maxDailyTrades;
            document.getElementById('maxDailyLoss').value = this.autoTradingSettings.maxDailyLoss;
            document.getElementById('aiConfidenceThreshold').value = this.autoTradingSettings.aiConfidenceThreshold;
            document.getElementById('aiConfidenceValue').textContent = Math.round(this.autoTradingSettings.aiConfidenceThreshold * 100) + '%';
        }
    }

    /**
     * تحديث واجهة المستخدم
     */
    updateUI() {
        // تحديث دوري كل ثانية
        setInterval(() => {
            this.updateOpenTradesCountdown();
        }, 1000);
    }

    /**
     * تحديث عداد الصفقات المفتوحة
     */
    updateOpenTradesCountdown() {
        this.openTrades.forEach((trade, id) => {
            const countdownElement = document.getElementById(`countdown-${id}`);
            if (countdownElement) {
                countdownElement.textContent = this.formatTimeRemaining(trade.expirationTime);
            }
        });
    }

    /**
     * بدء التحديثات الدورية
     */
    startPeriodicUpdates() {
        // طلب تحديث البيانات كل 30 ثانية
        setInterval(() => {
            if (this.isConnected) {
                this.socket.emit('requestDataUpdate');
            }
        }, 30000);
    }
}

// دوال عامة للواجهة
function refreshPairs() {
    if (window.dashboard && window.dashboard.socket) {
        window.dashboard.socket.emit('refreshInstruments');
        window.dashboard.addLogEntry('طلب تحديث الأزواج', 'info');
    }
}

function sortPairsByProfit() {
    // ترتيب الجدول حسب نسبة الربح
    const table = document.getElementById('pairsTableBody');
    const rows = Array.from(table.rows);

    rows.sort((a, b) => {
        const profitA = parseFloat(a.cells[2].textContent.replace('%', '')) || 0;
        const profitB = parseFloat(b.cells[2].textContent.replace('%', '')) || 0;
        return profitB - profitA;
    });

    rows.forEach(row => table.appendChild(row));
}

function manualTrade(asset, direction) {
    if (window.dashboard) {
        window.dashboard.manualTrade(asset, direction);
    }
}

function resetDailyStats() {
    if (window.dashboard && window.dashboard.socket) {
        window.dashboard.socket.emit('resetDailyStats');
        window.dashboard.addLogEntry('إعادة تعيين الإحصائيات اليومية', 'warning');
    }
}

function exportTradingData() {
    if (window.dashboard && window.dashboard.socket) {
        window.dashboard.socket.emit('exportTradingData');
        window.dashboard.addLogEntry('طلب تصدير بيانات التداول', 'info');
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new AdvancedTradingDashboard();
});
