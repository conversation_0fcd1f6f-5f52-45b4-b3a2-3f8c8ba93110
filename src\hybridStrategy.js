/**
 * الاستراتيجية الهجينة المتقدمة للسكالبينغ
 * Advanced Hybrid Scalping Strategy
 */

const TechnicalIndicators = require('./indicators');
const AIEngine = require('./aiEngine');
const EventEmitter = require('events');

class HybridStrategy extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.settings = {
            // إعدادات الطبقة الفنية
            technical: {
                sma_period: 10,
                ema_periods: [5, 10, 21],
                rsi_period: 5,
                rsi_oversold: 30,
                rsi_overbought: 70,
                macd_fast: 12,
                macd_slow: 26,
                macd_signal: 9,
                bb_period: 20,
                bb_deviation: 2,
                momentum_period: 10,
                atr_period: 5,
                minimum_signals: 2
            },
            
            // إعدادات الطبقة الكمية
            quantitative: {
                zscore_threshold: 2.0,
                probability_threshold: 0.7,
                volatility_min: 0.0001,
                volatility_max: 0.01,
                sharpe_threshold: 1.0,
                correlation_threshold: 0.6
            },
            
            // إعدادات الطبقة السلوكية
            behavioral: {
                pattern_strength_min: 'medium',
                volume_spike_threshold: 1.5,
                fear_greed_weight: 0.3,
                reversal_confirmation: true,
                time_pattern_weight: 0.2
            },
            
            // إعدادات الطبقة الذكية
            ai: {
                confidence_threshold: 0.8,
                model_weight: 0.4,
                ensemble_voting: true,
                prediction_horizon: 3,
                market_state_weight: 0.3
            },
            
            // إعدادات القرار النهائي
            decision: {
                minimum_confidence: 0.8,
                layer_weights: {
                    technical: 0.3,
                    quantitative: 0.25,
                    behavioral: 0.2,
                    ai: 0.25
                },
                time_filters: {
                    avoid_first_5min: true,
                    avoid_last_5min: true,
                    avoid_news_time: true
                }
            },
            
            ...options
        };

        // تخزين البيانات والتحليلات
        this.data = {
            candles: [],
            indicators: {},
            patterns: [],
            signals: [],
            analysis: {}
        };

        // إحصائيات الأداء
        this.performance = {
            total_signals: 0,
            successful_signals: 0,
            layer_accuracy: {
                technical: { correct: 0, total: 0 },
                quantitative: { correct: 0, total: 0 },
                behavioral: { correct: 0, total: 0 },
                ai: { correct: 0, total: 0 }
            },
            last_analysis: null
        };

        // محرك الذكاء الاصطناعي
        this.aiEngine = new AIEngine({
            modelDir: './models',
            dataDir: './data/ai',
            sequenceLength: 60,
            features: 15,
            batchSize: 32,
            epochs: 50
        });

        // تهيئة محرك الذكاء الاصطناعي
        this.initializeAI();
    }

    /**
     * تهيئة محرك الذكاء الاصطناعي
     */
    async initializeAI() {
        try {
            await this.aiEngine.initialize();
            console.log('✅ AI Engine initialized in Hybrid Strategy');
        } catch (error) {
            console.error('❌ Failed to initialize AI Engine:', error);
        }
    }

    /**
     * تحليل شامل للسوق - النقطة الرئيسية
     */
    async analyzeMarket(assetId, candles, additionalData = {}) {
        try {
            console.log(`🔍 Starting hybrid analysis for asset ${assetId}...`);
            
            if (!candles || candles.length < 30) {
                throw new Error('Insufficient candle data for analysis');
            }

            // تحديث البيانات
            this.data.candles = candles;
            this.data.assetId = assetId;
            this.data.timestamp = new Date();

            // الطبقة 1: التحليل الفني
            const technicalLayer = await this.analyzeTechnicalLayer(candles);
            
            // الطبقة 2: التحليل الكمي
            const quantitativeLayer = await this.analyzeQuantitativeLayer(candles, technicalLayer);
            
            // الطبقة 3: التحليل السلوكي
            const behavioralLayer = await this.analyzeBehavioralLayer(candles, additionalData);
            
            // الطبقة 4: الذكاء الاصطناعي
            const aiLayer = await this.analyzeAILayer(candles, {
                technical: technicalLayer,
                quantitative: quantitativeLayer,
                behavioral: behavioralLayer
            });

            // دمج النتائج واتخاذ القرار النهائي
            const finalDecision = this.makeFinalDecision({
                technical: technicalLayer,
                quantitative: quantitativeLayer,
                behavioral: behavioralLayer,
                ai: aiLayer
            });

            // حفظ التحليل
            this.data.analysis = {
                assetId: assetId,
                timestamp: new Date(),
                layers: {
                    technical: technicalLayer,
                    quantitative: quantitativeLayer,
                    behavioral: behavioralLayer,
                    ai: aiLayer
                },
                decision: finalDecision
            };

            this.performance.total_signals++;
            this.performance.last_analysis = new Date();

            console.log(`✅ Hybrid analysis completed for asset ${assetId}`);
            this.emit('analysisCompleted', this.data.analysis);

            return finalDecision;

        } catch (error) {
            console.error('❌ Error in hybrid analysis:', error);
            this.emit('analysisError', error);
            throw error;
        }
    }

    /**
     * الطبقة 1: التحليل الفني المتقدم
     */
    async analyzeTechnicalLayer(candles) {
        console.log('📊 Analyzing Technical Layer...');
        
        const closes = candles.map(c => c.close);
        const highs = candles.map(c => c.high);
        const lows = candles.map(c => c.low);
        const volumes = candles.map(c => c.volume || 0);

        // حساب المؤشرات الفنية
        const indicators = {
            sma: TechnicalIndicators.sma(closes, this.settings.technical.sma_period),
            ema5: TechnicalIndicators.ema(closes, this.settings.technical.ema_periods[0]),
            ema10: TechnicalIndicators.ema(closes, this.settings.technical.ema_periods[1]),
            ema21: TechnicalIndicators.ema(closes, this.settings.technical.ema_periods[2]),
            rsi: TechnicalIndicators.rsi(closes, this.settings.technical.rsi_period),
            macd: TechnicalIndicators.macd(closes, this.settings.technical.macd_fast, this.settings.technical.macd_slow, this.settings.technical.macd_signal),
            bb: TechnicalIndicators.bollingerBands(closes, this.settings.technical.bb_period, this.settings.technical.bb_deviation),
            momentum: this.calculateMomentum(closes, this.settings.technical.momentum_period),
            atr: TechnicalIndicators.atr(highs, lows, closes, this.settings.technical.atr_period),
            heiken_ashi: this.calculateHeikenAshi(candles)
        };

        this.data.indicators = indicators;

        // تحليل الإشارات الفنية
        const signals = [];
        
        // إشارة تقاطع EMA
        if (this.checkEMACrossover(indicators.ema5, indicators.ema10)) {
            signals.push({
                type: 'ema_crossover',
                direction: this.getEMACrossoverDirection(indicators.ema5, indicators.ema10),
                strength: 0.7,
                reason: 'EMA 5/10 crossover detected'
            });
        }

        // إشارة RSI
        const rsiSignal = this.analyzeRSI(indicators.rsi);
        if (rsiSignal) {
            signals.push(rsiSignal);
        }

        // إشارة MACD
        const macdSignal = this.analyzeMACD(indicators.macd);
        if (macdSignal) {
            signals.push(macdSignal);
        }

        // إشارة Bollinger Bands
        const bbSignal = this.analyzeBollingerBands(indicators.bb, closes);
        if (bbSignal) {
            signals.push(bbSignal);
        }

        // إشارة Momentum
        const momentumSignal = this.analyzeMomentum(indicators.momentum);
        if (momentumSignal) {
            signals.push(momentumSignal);
        }

        // تقييم الإشارات الفنية
        const technicalScore = this.calculateTechnicalScore(signals);
        const direction = this.determineTechnicalDirection(signals);
        const confidence = this.calculateTechnicalConfidence(signals, indicators);

        return {
            layer: 'technical',
            indicators: indicators,
            signals: signals,
            score: technicalScore,
            direction: direction,
            confidence: confidence,
            valid: signals.length >= this.settings.technical.minimum_signals,
            timestamp: new Date()
        };
    }

    /**
     * الطبقة 2: التحليل الكمي المتقدم
     */
    async analyzeQuantitativeLayer(candles, technicalLayer) {
        console.log('📈 Analyzing Quantitative Layer...');
        
        const closes = candles.map(c => c.close);
        
        // حساب Z-Score
        const zScore = this.calculateZScore(closes);
        
        // تحليل الاحتمالات التاريخية
        const probabilityFilter = await this.calculateProbabilityFilter(technicalLayer.signals);
        
        // تحليل التقلبات
        const volatilityAnalysis = this.analyzeVolatility(candles);
        
        // حساب نسبة شارب
        const sharpeRatio = this.calculateSharpeRatio(closes);
        
        // تحليل الارتباط
        const correlationAnalysis = this.analyzeCorrelation(technicalLayer.indicators);

        // تقييم الفلاتر الكمية
        const filters = {
            zscore: Math.abs(zScore) > this.settings.quantitative.zscore_threshold,
            probability: probabilityFilter > this.settings.quantitative.probability_threshold,
            volatility: volatilityAnalysis.suitable,
            sharpe: sharpeRatio > this.settings.quantitative.sharpe_threshold,
            correlation: correlationAnalysis.strength > this.settings.quantitative.correlation_threshold
        };

        const passedFilters = Object.values(filters).filter(f => f).length;
        const totalFilters = Object.keys(filters).length;
        const quantitativeScore = passedFilters / totalFilters;

        return {
            layer: 'quantitative',
            metrics: {
                zScore: zScore,
                probability: probabilityFilter,
                volatility: volatilityAnalysis,
                sharpe: sharpeRatio,
                correlation: correlationAnalysis
            },
            filters: filters,
            score: quantitativeScore,
            valid: quantitativeScore > 0.6,
            enhancement: quantitativeScore > 0.8 ? 'strong' : quantitativeScore > 0.6 ? 'moderate' : 'weak',
            timestamp: new Date()
        };
    }

    /**
     * الطبقة 3: التحليل السلوكي المتقدم
     */
    async analyzeBehavioralLayer(candles, additionalData = {}) {
        console.log('🧠 Analyzing Behavioral Layer...');
        
        // تحليل أنماط الشموع
        const candlestickPatterns = TechnicalIndicators.detectCandlestickPatterns(candles.slice(-10));
        
        // تحليل الزخم السلوكي
        const momentumBehavior = this.analyzeMomentumBehavior(candles);
        
        // تحليل الخوف والطمع
        const fearGreedAnalysis = this.analyzeFearGreed(candles, additionalData);
        
        // تحليل الأنماط الزمنية
        const timePatterns = this.analyzeTimePatterns(candles);
        
        // تحليل الضغط العكسي
        const reversalPressure = this.analyzeReversalPressure(candles);

        // تقييم السلوك العام
        const behaviorSignals = [];
        
        // إشارات أنماط الشموع
        candlestickPatterns.forEach(pattern => {
            if (pattern.strength === 'strong' || pattern.strength === 'very_strong') {
                behaviorSignals.push({
                    type: 'candlestick_pattern',
                    pattern: pattern.pattern,
                    direction: this.getPatternDirection(pattern),
                    strength: this.getPatternStrength(pattern),
                    reason: `${pattern.pattern} pattern detected`
                });
            }
        });

        // إشارات الزخم السلوكي
        if (momentumBehavior.signal) {
            behaviorSignals.push(momentumBehavior.signal);
        }

        // إشارات الخوف والطمع
        if (fearGreedAnalysis.signal) {
            behaviorSignals.push(fearGreedAnalysis.signal);
        }

        const behavioralScore = this.calculateBehavioralScore(behaviorSignals, {
            patterns: candlestickPatterns,
            momentum: momentumBehavior,
            fearGreed: fearGreedAnalysis,
            timePatterns: timePatterns,
            reversal: reversalPressure
        });

        return {
            layer: 'behavioral',
            patterns: candlestickPatterns,
            momentum: momentumBehavior,
            fearGreed: fearGreedAnalysis,
            timePatterns: timePatterns,
            reversalPressure: reversalPressure,
            signals: behaviorSignals,
            score: behavioralScore,
            valid: behaviorSignals.length > 0,
            timestamp: new Date()
        };
    }

    /**
     * الطبقة 4: الذكاء الاصطناعي المتقدم - محرك حقيقي
     */
    async analyzeAILayer(candles, layerResults) {
        console.log('🤖 Analyzing AI Layer with Real AI Engine...');

        try {
            // التحقق من تهيئة محرك الذكاء الاصطناعي
            if (!this.aiEngine.isInitialized) {
                console.log('⚠️ AI Engine not initialized, using fallback analysis');
                return this.fallbackAIAnalysis(candles, layerResults);
            }

            // تحضير المؤشرات للذكاء الاصطناعي
            const indicators = this.prepareIndicatorsForAI(candles, layerResults);

            // التنبؤ باستخدام محرك الذكاء الاصطناعي الحقيقي
            const aiPrediction = await this.aiEngine.predict(candles, indicators);

            if (!aiPrediction) {
                console.log('⚠️ AI prediction failed, using fallback');
                return this.fallbackAIAnalysis(candles, layerResults);
            }

            // تصنيف حالة السوق
            const marketState = this.classifyMarketStateFromAI(aiPrediction, layerResults);

            // تحليل التوافق مع الطبقات الأخرى
            const layerConsensus = this.analyzeLayerConsensus(layerResults, {
                direction: aiPrediction.direction,
                probability: aiPrediction.confidence
            });

            // حساب الثقة النهائية
            const finalConfidence = this.calculateFinalAIConfidence(aiPrediction, marketState, layerConsensus);

            return {
                layer: 'ai',
                prediction: {
                    direction: aiPrediction.direction,
                    probability: aiPrediction.confidence,
                    ensemble: aiPrediction.ensemble,
                    models: aiPrediction.predictions,
                    horizon: this.settings.ai.prediction_horizon
                },
                marketState: marketState,
                confidence: finalConfidence,
                consensus: layerConsensus,
                aiStats: this.aiEngine.getPerformanceStats(),
                valid: finalConfidence > this.settings.ai.confidence_threshold,
                timestamp: new Date(),
                isRealAI: true
            };

        } catch (error) {
            console.error('❌ Error in AI Layer analysis:', error);
            return this.fallbackAIAnalysis(candles, layerResults);
        }
    }

    /**
     * تحضير المؤشرات للذكاء الاصطناعي
     */
    prepareIndicatorsForAI(candles, layerResults) {
        const indicators = [];

        // استخراج المؤشرات من الطبقة الفنية
        const technical = layerResults.technical || {};
        const technicalIndicators = technical.indicators || {};

        for (let i = 0; i < candles.length; i++) {
            indicators.push({
                sma: technicalIndicators.sma?.[i] || candles[i].close,
                ema5: technicalIndicators.ema5?.[i] || candles[i].close,
                ema10: technicalIndicators.ema10?.[i] || candles[i].close,
                ema21: technicalIndicators.ema21?.[i] || candles[i].close,
                rsi5: technicalIndicators.rsi5?.[i] || 50,
                rsi14: technicalIndicators.rsi14?.[i] || 50,
                macd: technicalIndicators.macd?.[i] || 0,
                macdSignal: technicalIndicators.macdSignal?.[i] || 0,
                macdHistogram: technicalIndicators.macdHistogram?.[i] || 0,
                momentum: technicalIndicators.momentum?.[i] || 0
            });
        }

        return indicators;
    }

    /**
     * تصنيف حالة السوق من نتائج الذكاء الاصطناعي
     */
    classifyMarketStateFromAI(aiPrediction, layerResults) {
        const confidence = aiPrediction.confidence;
        const direction = aiPrediction.direction;

        // تحليل التقلبات من الطبقة الكمية
        const quantitative = layerResults.quantitative || {};
        const volatility = quantitative.volatility || { level: 'medium' };

        if (confidence > 0.8 && direction !== 'neutral') {
            if (volatility.level === 'high') {
                return 'volatile_trending';
            } else {
                return 'trending';
            }
        } else if (confidence > 0.6) {
            return 'weak_trending';
        } else {
            return 'ranging';
        }
    }

    /**
     * حساب الثقة النهائية للذكاء الاصطناعي
     */
    calculateFinalAIConfidence(aiPrediction, marketState, layerConsensus) {
        let confidence = aiPrediction.confidence;

        // تعديل الثقة بناء على حالة السوق
        if (marketState === 'trending') {
            confidence += 0.1;
        } else if (marketState === 'volatile_trending') {
            confidence += 0.05;
        } else if (marketState === 'ranging') {
            confidence -= 0.1;
        }

        // تعديل الثقة بناء على توافق الطبقات
        if (layerConsensus.agreement > 0.7) {
            confidence += 0.1;
        } else if (layerConsensus.agreement < 0.3) {
            confidence -= 0.15;
        }

        // ضمان أن الثقة بين 0 و 1
        return Math.max(0, Math.min(1, confidence));
    }

    /**
     * تحليل احتياطي في حالة فشل الذكاء الاصطناعي
     */
    fallbackAIAnalysis(candles, layerResults) {
        console.log('🔄 Using fallback AI analysis...');

        // تحليل بسيط بناء على الطبقات الأخرى
        const technical = layerResults.technical || { valid: false };
        const quantitative = layerResults.quantitative || { valid: false };
        const behavioral = layerResults.behavioral || { valid: false };

        let direction = 'neutral';
        let confidence = 0.5;

        // تحديد الاتجاه بناء على الطبقات الأخرى
        const validLayers = [technical, quantitative, behavioral].filter(layer => layer.valid);

        if (validLayers.length > 0) {
            // حساب متوسط الاتجاهات
            const upVotes = validLayers.filter(layer =>
                layer.signal === 'buy' || layer.direction === 'up' || layer.score > 0.6
            ).length;

            const downVotes = validLayers.filter(layer =>
                layer.signal === 'sell' || layer.direction === 'down' || layer.score < -0.6
            ).length;

            if (upVotes > downVotes) {
                direction = 'up';
                confidence = 0.6 + (upVotes / validLayers.length) * 0.2;
            } else if (downVotes > upVotes) {
                direction = 'down';
                confidence = 0.6 + (downVotes / validLayers.length) * 0.2;
            }
        }

        return {
            layer: 'ai',
            prediction: {
                direction: direction,
                probability: confidence,
                horizon: this.settings.ai.prediction_horizon
            },
            marketState: 'unknown',
            confidence: confidence,
            consensus: { agreement: 0.5, conflicting: false },
            valid: confidence > this.settings.ai.confidence_threshold,
            timestamp: new Date(),
            isRealAI: false,
            fallback: true
        };
    }

    /**
     * تدريب محرك الذكاء الاصطناعي
     */
    async trainAI(historicalData) {
        try {
            console.log('🎓 Training AI Engine with historical data...');

            if (!this.aiEngine.isInitialized) {
                console.log('⚠️ AI Engine not initialized');
                return false;
            }

            // تحضير البيانات التاريخية للتدريب
            const trainingDatasets = [];

            for (const [symbol, data] of historicalData.entries()) {
                if (data.data && data.data.length > 100) {
                    // تحليل البيانات التاريخية للحصول على المؤشرات
                    const technicalAnalysis = await this.analyzeTechnicalLayer(data.data);
                    const indicators = this.prepareIndicatorsForAI(data.data, { technical: technicalAnalysis });

                    trainingDatasets.push({
                        symbol: symbol,
                        candles: data.data,
                        indicators: indicators
                    });
                }
            }

            if (trainingDatasets.length === 0) {
                console.log('⚠️ No suitable training data available');
                return false;
            }

            // دمج جميع البيانات للتدريب
            let allCandles = [];
            let allIndicators = [];

            trainingDatasets.forEach(dataset => {
                allCandles = allCandles.concat(dataset.candles);
                allIndicators = allIndicators.concat(dataset.indicators);
            });

            console.log(`📊 Training with ${allCandles.length} candles from ${trainingDatasets.length} symbols`);

            // بدء التدريب
            const trainingResult = await this.aiEngine.trainModels(allCandles, allIndicators);

            if (trainingResult) {
                console.log('✅ AI Engine training completed successfully');
                this.emit('aiTrainingCompleted', {
                    success: true,
                    dataPoints: allCandles.length,
                    symbols: trainingDatasets.length,
                    timestamp: new Date()
                });
                return true;
            } else {
                console.log('❌ AI Engine training failed');
                return false;
            }

        } catch (error) {
            console.error('❌ Error training AI Engine:', error);
            this.emit('aiTrainingError', error);
            return false;
        }
    }

    /**
     * تقييم أداء الذكاء الاصطناعي
     */
    evaluateAIPrediction(prediction, actualResult) {
        try {
            if (this.aiEngine && prediction.isRealAI) {
                this.aiEngine.evaluatePrediction(prediction, actualResult);
            }
        } catch (error) {
            console.error('❌ Error evaluating AI prediction:', error);
        }
    }

    /**
     * اتخاذ القرار النهائي
     */
    makeFinalDecision(layerResults) {
        console.log('⚖️ Making final decision...');

        // التأكد من وجود جميع الطبقات مع قيم افتراضية
        const technical = layerResults.technical || { valid: false, confidence: 0 };
        const quantitative = layerResults.quantitative || { valid: false, score: 0 };
        const behavioral = layerResults.behavioral || { valid: false, score: 0, signals: [] };
        const ai = layerResults.ai || { valid: false, confidence: 0, prediction: {} };

        const weights = this.settings.decision.layer_weights || {
            technical: 0.3,
            quantitative: 0.25,
            behavioral: 0.2,
            ai: 0.25
        };

        // حساب النتيجة المرجحة
        let weightedScore = 0;
        let totalWeight = 0;
        let direction = null;
        let reasons = [];

        // تقييم كل طبقة
        if (technical.valid) {
            weightedScore += technical.confidence * weights.technical;
            totalWeight += weights.technical;
            if (technical.direction) {
                direction = technical.direction;
                reasons.push(`Technical analysis suggests ${technical.direction}`);
            }
        }

        if (quantitative.valid) {
            weightedScore += quantitative.score * weights.quantitative;
            totalWeight += weights.quantitative;
            reasons.push(`Quantitative filters passed with score ${quantitative.score.toFixed(2)}`);
        }

        if (behavioral.valid) {
            weightedScore += behavioral.score * weights.behavioral;
            totalWeight += weights.behavioral;
            reasons.push(`Behavioral analysis confirms with ${behavioral.signals ? behavioral.signals.length : 0} signals`);
        }

        if (ai.valid) {
            weightedScore += ai.confidence * weights.ai;
            totalWeight += weights.ai;
            if (ai.prediction.direction && (!direction || ai.consensus.agreement > 0.7)) {
                direction = ai.prediction.direction;
                reasons.push(`AI predicts ${ai.prediction.direction} with ${(ai.confidence * 100).toFixed(1)}% confidence`);
            }
        }

        // حساب الثقة النهائية
        const finalConfidence = totalWeight > 0 ? weightedScore / totalWeight : 0;

        // فحص الفلاتر الزمنية
        const timeFilter = this.checkTimeFilters();

        // القرار النهائي
        const decision = {
            signal: direction && finalConfidence >= this.settings.decision.minimum_confidence && timeFilter.allowed,
            direction: direction,
            confidence: finalConfidence,
            reasons: reasons,
            timeFilter: timeFilter,
            layerSummary: {
                technical: { valid: technical.valid, score: technical.confidence || 0 },
                quantitative: { valid: quantitative.valid, score: quantitative.score || 0 },
                behavioral: { valid: behavioral.valid, score: behavioral.score || 0 },
                ai: { valid: ai.valid, score: ai.confidence || 0 }
            },
            recommendation: this.generateRecommendation(finalConfidence, direction, layerResults),
            timestamp: new Date()
        };

        console.log(`🎯 Final decision: ${decision.signal ? `${direction} with ${(finalConfidence * 100).toFixed(1)}% confidence` : 'No signal'}`);

        return decision;
    }

    /**
     * دوال مساعدة للتحليل الفني
     */
    checkEMACrossover(ema5, ema10) {
        if (ema5.length < 2 || ema10.length < 2) return false;

        const current5 = ema5[ema5.length - 1];
        const current10 = ema10[ema10.length - 1];
        const prev5 = ema5[ema5.length - 2];
        const prev10 = ema10[ema10.length - 2];

        return (current5 > current10 && prev5 <= prev10) || (current5 < current10 && prev5 >= prev10);
    }

    getEMACrossoverDirection(ema5, ema10) {
        const current5 = ema5[ema5.length - 1];
        const current10 = ema10[ema10.length - 1];
        return current5 > current10 ? 'call' : 'put';
    }

    analyzeRSI(rsi) {
        if (rsi.length === 0) return null;

        const currentRSI = rsi[rsi.length - 1];
        const prevRSI = rsi.length > 1 ? rsi[rsi.length - 2] : currentRSI;

        if (currentRSI < this.settings.technical.rsi_oversold && prevRSI >= this.settings.technical.rsi_oversold) {
            return {
                type: 'rsi_oversold',
                direction: 'call',
                strength: 0.8,
                reason: 'RSI oversold reversal signal'
            };
        }

        if (currentRSI > this.settings.technical.rsi_overbought && prevRSI <= this.settings.technical.rsi_overbought) {
            return {
                type: 'rsi_overbought',
                direction: 'put',
                strength: 0.8,
                reason: 'RSI overbought reversal signal'
            };
        }

        return null;
    }

    analyzeMACD(macd) {
        if (macd.macd.length < 2 || macd.signal.length < 2) return null;

        const currentMACD = macd.macd[macd.macd.length - 1];
        const currentSignal = macd.signal[macd.signal.length - 1];
        const prevMACD = macd.macd[macd.macd.length - 2];
        const prevSignal = macd.signal[macd.signal.length - 2];

        if (currentMACD > currentSignal && prevMACD <= prevSignal) {
            return {
                type: 'macd_bullish',
                direction: 'call',
                strength: 0.75,
                reason: 'MACD bullish crossover'
            };
        }

        if (currentMACD < currentSignal && prevMACD >= prevSignal) {
            return {
                type: 'macd_bearish',
                direction: 'put',
                strength: 0.75,
                reason: 'MACD bearish crossover'
            };
        }

        return null;
    }

    analyzeBollingerBands(bb, closes) {
        if (bb.upper.length === 0 || closes.length === 0) return null;

        const currentPrice = closes[closes.length - 1];
        const upperBand = bb.upper[bb.upper.length - 1];
        const lowerBand = bb.lower[bb.lower.length - 1];

        if (currentPrice <= lowerBand) {
            return {
                type: 'bb_lower_touch',
                direction: 'call',
                strength: 0.65,
                reason: 'Price touched lower Bollinger Band'
            };
        }

        if (currentPrice >= upperBand) {
            return {
                type: 'bb_upper_touch',
                direction: 'put',
                strength: 0.65,
                reason: 'Price touched upper Bollinger Band'
            };
        }

        return null;
    }

    calculateMomentum(closes, period) {
        const momentum = [];
        for (let i = period; i < closes.length; i++) {
            momentum.push(closes[i] - closes[i - period]);
        }
        return momentum;
    }

    analyzeMomentum(momentum) {
        if (momentum.length < 2) return null;

        const current = momentum[momentum.length - 1];
        const prev = momentum[momentum.length - 2];

        if (current > 0 && prev <= 0) {
            return {
                type: 'momentum_positive',
                direction: 'call',
                strength: 0.6,
                reason: 'Positive momentum shift'
            };
        }

        if (current < 0 && prev >= 0) {
            return {
                type: 'momentum_negative',
                direction: 'put',
                strength: 0.6,
                reason: 'Negative momentum shift'
            };
        }

        return null;
    }

    calculateHeikenAshi(candles) {
        const ha = [];

        for (let i = 0; i < candles.length; i++) {
            const candle = candles[i];

            if (i === 0) {
                ha.push({
                    open: (candle.open + candle.close) / 2,
                    close: (candle.open + candle.high + candle.low + candle.close) / 4,
                    high: candle.high,
                    low: candle.low
                });
            } else {
                const prevHA = ha[i - 1];
                const haClose = (candle.open + candle.high + candle.low + candle.close) / 4;
                const haOpen = (prevHA.open + prevHA.close) / 2;

                ha.push({
                    open: haOpen,
                    close: haClose,
                    high: Math.max(candle.high, haOpen, haClose),
                    low: Math.min(candle.low, haOpen, haClose)
                });
            }
        }

        return ha;
    }

    calculateTechnicalScore(signals) {
        if (signals.length === 0) return 0;

        const totalStrength = signals.reduce((sum, signal) => sum + signal.strength, 0);
        return totalStrength / signals.length;
    }

    determineTechnicalDirection(signals) {
        const callSignals = signals.filter(s => s.direction === 'call');
        const putSignals = signals.filter(s => s.direction === 'put');

        if (callSignals.length > putSignals.length) return 'call';
        if (putSignals.length > callSignals.length) return 'put';

        // في حالة التعادل، نختار الأقوى
        const callStrength = callSignals.reduce((sum, s) => sum + s.strength, 0);
        const putStrength = putSignals.reduce((sum, s) => sum + s.strength, 0);

        return callStrength > putStrength ? 'call' : 'put';
    }

    calculateTechnicalConfidence(signals, indicators) {
        if (signals.length === 0) return 0;

        let confidence = this.calculateTechnicalScore(signals);

        // تعزيز الثقة بناء على توافق المؤشرات
        const emaAlignment = this.checkEMAAlignment(indicators);
        if (emaAlignment) confidence += 0.1;

        // تعزيز الثقة بناء على قوة الزخم
        const strongMomentum = this.checkStrongMomentum(indicators);
        if (strongMomentum) confidence += 0.1;

        return Math.min(confidence, 1.0);
    }

    checkEMAAlignment(indicators) {
        if (!indicators.ema5 || !indicators.ema10 || !indicators.ema21) return false;

        const ema5 = indicators.ema5[indicators.ema5.length - 1];
        const ema10 = indicators.ema10[indicators.ema10.length - 1];
        const ema21 = indicators.ema21[indicators.ema21.length - 1];

        return (ema5 > ema10 && ema10 > ema21) || (ema5 < ema10 && ema10 < ema21);
    }

    checkStrongMomentum(indicators) {
        if (!indicators.momentum || indicators.momentum.length === 0) return false;

        const momentum = indicators.momentum[indicators.momentum.length - 1];
        return Math.abs(momentum) > 0.001; // عتبة الزخم القوي
    }

    /**
     * دوال مساعدة للتحليل السلوكي
     */
    analyzeMomentumBehavior(candles) {
        if (candles.length < 5) return { signal: null };

        const recent = candles.slice(-5);
        let bullishMomentum = 0;
        let bearishMomentum = 0;

        for (let i = 1; i < recent.length; i++) {
            const prev = recent[i - 1];
            const curr = recent[i];

            if (curr.close > prev.close) {
                bullishMomentum += (curr.close - prev.close) / prev.close;
            } else {
                bearishMomentum += (prev.close - curr.close) / prev.close;
            }
        }

        const signal = bullishMomentum > bearishMomentum * 1.5 ? {
            type: 'momentum_bullish',
            direction: 'call',
            strength: 0.6,
            reason: 'Strong bullish momentum detected'
        } : bearishMomentum > bullishMomentum * 1.5 ? {
            type: 'momentum_bearish',
            direction: 'put',
            strength: 0.6,
            reason: 'Strong bearish momentum detected'
        } : null;

        return { signal: signal };
    }

    analyzeFearGreed(candles, additionalData = {}) {
        if (candles.length < 10) return { signal: null };

        const recent = candles.slice(-10);
        let fearIndex = 0;
        let greedIndex = 0;

        // تحليل مبسط للخوف والطمع بناء على الشموع
        recent.forEach(candle => {
            const bodySize = Math.abs(candle.close - candle.open);
            const totalSize = candle.high - candle.low;
            const bodyRatio = bodySize / totalSize;

            if (candle.close < candle.open && bodyRatio > 0.7) {
                fearIndex += 1; // شمعة خوف قوية
            } else if (candle.close > candle.open && bodyRatio > 0.7) {
                greedIndex += 1; // شمعة طمع قوية
            }
        });

        const signal = fearIndex > 6 ? {
            type: 'fear_oversold',
            direction: 'call',
            strength: 0.5,
            reason: 'Market fear suggests oversold condition'
        } : greedIndex > 6 ? {
            type: 'greed_overbought',
            direction: 'put',
            strength: 0.5,
            reason: 'Market greed suggests overbought condition'
        } : null;

        return { signal: signal };
    }

    analyzeTimePatterns(candles) {
        // تحليل مبسط للأنماط الزمنية
        const now = new Date();
        const hour = now.getHours();
        const minute = now.getMinutes();

        // أنماط زمنية شائعة في السوق
        const patterns = {
            londonOpen: hour === 8 && minute < 30, // افتتاح لندن
            nyOpen: hour === 13 && minute < 30,    // افتتاح نيويورك
            asiaClose: hour === 9 && minute < 30   // إغلاق آسيا
        };

        return {
            patterns: patterns,
            isSignificantTime: Object.values(patterns).some(p => p)
        };
    }

    analyzeReversalPressure(candles) {
        if (candles.length < 3) return { pressure: 'none' };

        const recent = candles.slice(-3);
        let reversalSignals = 0;

        recent.forEach(candle => {
            const upperShadow = candle.high - Math.max(candle.open, candle.close);
            const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
            const body = Math.abs(candle.close - candle.open);

            // ظلال طويلة تشير لضغط عكسي
            if (upperShadow > body * 2 || lowerShadow > body * 2) {
                reversalSignals++;
            }
        });

        return {
            pressure: reversalSignals >= 2 ? 'high' : reversalSignals === 1 ? 'medium' : 'low',
            signals: reversalSignals
        };
    }

    getPatternDirection(pattern) {
        const bullishPatterns = ['hammer', 'bullish_engulfing', 'morning_star'];
        const bearishPatterns = ['shooting_star', 'bearish_engulfing', 'evening_star'];

        if (bullishPatterns.includes(pattern.pattern)) return 'call';
        if (bearishPatterns.includes(pattern.pattern)) return 'put';
        return null;
    }

    getPatternStrength(pattern) {
        const strengthMap = {
            'very_strong': 0.9,
            'strong': 0.8,
            'medium': 0.6,
            'weak': 0.4
        };
        return strengthMap[pattern.strength] || 0.5;
    }

    calculateBehavioralScore(signals, analysis) {
        if (signals.length === 0) return 0;

        let score = 0;
        let totalWeight = 0;

        // تقييم الإشارات
        signals.forEach(signal => {
            score += signal.strength;
            totalWeight += 1;
        });

        // تعزيز النتيجة بناء على التحليلات الإضافية
        if (analysis.patterns && analysis.patterns.length > 0) {
            score += 0.1;
        }

        if (analysis.reversalPressure && analysis.reversalPressure.pressure === 'high') {
            score += 0.1;
        }

        if (analysis.timePatterns && analysis.timePatterns.isSignificantTime) {
            score += 0.05;
        }

        return totalWeight > 0 ? Math.min(score / totalWeight, 1.0) : 0;
    }

    /**
     * دوال مساعدة للذكاء الاصطناعي
     */
    prepareMLFeatures(candles, layerResults) {
        // تحضير الميزات للنموذج
        const recent = candles.slice(-20);
        const closes = recent.map(c => c.close);

        return {
            price_change: closes[closes.length - 1] - closes[0],
            volatility: this.calculateVolatility(recent),
            trend_strength: this.calculateTrendStrength(closes),
            technical_score: layerResults.technical.confidence || 0,
            quantitative_score: layerResults.quantitative.score || 0,
            behavioral_score: layerResults.behavioral.score || 0
        };
    }

    async predictDirection(features) {
        // محاكاة نموذج التعلم الآلي
        // في التطبيق الحقيقي، سيتم استخدام نموذج مدرب

        const score = (features.technical_score * 0.4) +
                     (features.quantitative_score * 0.3) +
                     (features.behavioral_score * 0.3);

        const direction = features.price_change > 0 ? 'call' : 'put';
        const probability = Math.min(0.5 + score * 0.4, 0.95);

        return {
            direction: direction,
            probability: probability
        };
    }

    async classifyMarketState(features) {
        // تصنيف حالة السوق
        if (features.volatility > 0.005) {
            return 'volatile';
        } else if (Math.abs(features.trend_strength) > 0.7) {
            return 'trending';
        } else {
            return 'ranging';
        }
    }

    calculateAIConfidence(prediction, marketState, layerResults) {
        let confidence = prediction.probability;

        // تعديل الثقة بناء على حالة السوق
        if (marketState === 'trending') {
            confidence += 0.1;
        } else if (marketState === 'volatile') {
            confidence -= 0.1;
        }

        // تعديل الثقة بناء على توافق الطبقات
        const layerAgreement = this.calculateLayerAgreement(layerResults);
        confidence += layerAgreement * 0.2;

        return Math.max(0, Math.min(1, confidence));
    }

    analyzeLayerConsensus(layerResults, prediction) {
        let agreement = 0;
        let totalLayers = 0;

        if (layerResults.technical.valid) {
            if (layerResults.technical.direction === prediction.direction) {
                agreement++;
            }
            totalLayers++;
        }

        if (layerResults.behavioral.valid) {
            const behavioralDirection = this.getBehavioralDirection(layerResults.behavioral);
            if (behavioralDirection === prediction.direction) {
                agreement++;
            }
            totalLayers++;
        }

        return {
            agreement: totalLayers > 0 ? agreement / totalLayers : 0,
            totalLayers: totalLayers,
            agreedLayers: agreement
        };
    }

    calculateVolatility(candles) {
        if (candles.length < 2) return 0;

        const returns = [];
        for (let i = 1; i < candles.length; i++) {
            returns.push((candles[i].close - candles[i - 1].close) / candles[i - 1].close);
        }

        const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
        const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / returns.length;

        return Math.sqrt(variance);
    }

    calculateTrendStrength(closes) {
        if (closes.length < 10) return 0;

        const first = closes[0];
        const last = closes[closes.length - 1];
        const change = (last - first) / first;

        // حساب قوة الاتجاه بناء على الاتساق
        let consistentMoves = 0;
        const direction = change > 0 ? 1 : -1;

        for (let i = 1; i < closes.length; i++) {
            const moveDirection = closes[i] > closes[i - 1] ? 1 : -1;
            if (moveDirection === direction) {
                consistentMoves++;
            }
        }

        const consistency = consistentMoves / (closes.length - 1);
        return change * consistency;
    }

    calculateLayerAgreement(layerResults) {
        let agreements = 0;
        let comparisons = 0;

        const layers = ['technical', 'quantitative', 'behavioral'];

        for (let i = 0; i < layers.length; i++) {
            for (let j = i + 1; j < layers.length; j++) {
                const layer1 = layerResults[layers[i]];
                const layer2 = layerResults[layers[j]];

                if (layer1.valid && layer2.valid) {
                    const score1 = layer1.confidence || layer1.score || 0;
                    const score2 = layer2.confidence || layer2.score || 0;

                    if (Math.abs(score1 - score2) < 0.3) {
                        agreements++;
                    }
                    comparisons++;
                }
            }
        }

        return comparisons > 0 ? agreements / comparisons : 0;
    }

    getBehavioralDirection(behavioralLayer) {
        if (!behavioralLayer.signals || behavioralLayer.signals.length === 0) {
            return null;
        }

        const callSignals = behavioralLayer.signals.filter(s => s.direction === 'call').length;
        const putSignals = behavioralLayer.signals.filter(s => s.direction === 'put').length;

        return callSignals > putSignals ? 'call' : putSignals > callSignals ? 'put' : null;
    }

    /**
     * دوال مساعدة للتحليل الكمي
     */
    calculateZScore(closes) {
        if (closes.length < 20) return 0;

        const recent = closes.slice(-20);
        const mean = recent.reduce((sum, val) => sum + val, 0) / recent.length;
        const variance = recent.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / recent.length;
        const stdDev = Math.sqrt(variance);

        const currentPrice = closes[closes.length - 1];
        return stdDev > 0 ? (currentPrice - mean) / stdDev : 0;
    }

    async calculateProbabilityFilter(signals) {
        // محاكاة حساب الاحتمالات بناء على البيانات التاريخية
        // في التطبيق الحقيقي، سيتم استخدام قاعدة بيانات للنتائج السابقة

        if (signals.length === 0) return 0;

        // حساب مبسط للاحتمالية
        const signalTypes = signals.map(s => s.type);
        const uniqueTypes = [...new Set(signalTypes)];

        // افتراض نسب نجاح مختلفة لكل نوع إشارة
        const successRates = {
            'ema_crossover': 0.72,
            'rsi_oversold': 0.68,
            'rsi_overbought': 0.68,
            'macd_bullish': 0.75,
            'macd_bearish': 0.75,
            'bb_lower_touch': 0.65,
            'bb_upper_touch': 0.65,
            'momentum_positive': 0.60,
            'momentum_negative': 0.60
        };

        const avgSuccessRate = uniqueTypes.reduce((sum, type) => {
            return sum + (successRates[type] || 0.5);
        }, 0) / uniqueTypes.length;

        return avgSuccessRate;
    }

    analyzeVolatility(candles) {
        if (candles.length < 10) return { suitable: false, value: 0 };

        const recent = candles.slice(-10);
        const ranges = recent.map(c => (c.high - c.low) / c.close);
        const avgRange = ranges.reduce((sum, r) => sum + r, 0) / ranges.length;

        const suitable = avgRange >= this.settings.quantitative.volatility_min &&
                         avgRange <= this.settings.quantitative.volatility_max;

        return {
            suitable: suitable,
            value: avgRange,
            level: avgRange > 0.005 ? 'high' : avgRange > 0.002 ? 'medium' : 'low'
        };
    }

    calculateSharpeRatio(closes) {
        if (closes.length < 20) return 0;

        const returns = [];
        for (let i = 1; i < closes.length; i++) {
            returns.push((closes[i] - closes[i - 1]) / closes[i - 1]);
        }

        const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
        const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
        const stdDev = Math.sqrt(variance);

        return stdDev > 0 ? avgReturn / stdDev : 0;
    }

    analyzeCorrelation(indicators) {
        // تحليل مبسط للارتباط بين المؤشرات
        let correlationCount = 0;
        let totalChecks = 0;

        // فحص توافق اتجاهات المؤشرات
        if (indicators.ema5 && indicators.ema10) {
            const ema5Trend = this.getTrend(indicators.ema5);
            const ema10Trend = this.getTrend(indicators.ema10);
            if (ema5Trend === ema10Trend) correlationCount++;
            totalChecks++;
        }

        if (indicators.rsi && indicators.momentum) {
            const rsiTrend = this.getRSITrend(indicators.rsi);
            const momentumTrend = this.getMomentumTrend(indicators.momentum);
            if (rsiTrend === momentumTrend) correlationCount++;
            totalChecks++;
        }

        const strength = totalChecks > 0 ? correlationCount / totalChecks : 0;

        return {
            strength: strength,
            level: strength > 0.7 ? 'strong' : strength > 0.5 ? 'moderate' : 'weak'
        };
    }

    getTrend(values) {
        if (values.length < 3) return 'neutral';

        const recent = values.slice(-3);
        const isUptrend = recent[2] > recent[1] && recent[1] > recent[0];
        const isDowntrend = recent[2] < recent[1] && recent[1] < recent[0];

        return isUptrend ? 'up' : isDowntrend ? 'down' : 'neutral';
    }

    getRSITrend(rsi) {
        if (rsi.length < 2) return 'neutral';

        const current = rsi[rsi.length - 1];
        const prev = rsi[rsi.length - 2];

        return current > prev ? 'up' : current < prev ? 'down' : 'neutral';
    }

    getMomentumTrend(momentum) {
        if (momentum.length < 2) return 'neutral';

        const current = momentum[momentum.length - 1];
        const prev = momentum[momentum.length - 2];

        return current > prev ? 'up' : current < prev ? 'down' : 'neutral';
    }

    /**
     * فحص الفلاتر الزمنية
     */
    checkTimeFilters() {
        const now = new Date();
        const minutes = now.getMinutes();
        const hour = now.getHours();

        const filters = this.settings.decision.time_filters;
        const reasons = [];

        // تجنب أول 5 دقائق من كل ساعة
        if (filters.avoid_first_5min && minutes < 5) {
            reasons.push('First 5 minutes of hour');
        }

        // تجنب آخر 5 دقائق من كل ساعة
        if (filters.avoid_last_5min && minutes > 54) {
            reasons.push('Last 5 minutes of hour');
        }

        // تجنب ساعات الأخبار (مثال: 8:30, 10:00, 14:30)
        if (filters.avoid_news_time) {
            const newsHours = [8, 10, 14, 16];
            const newsMinutes = [0, 30];

            if (newsHours.includes(hour) && newsMinutes.includes(minutes)) {
                reasons.push('News time');
            }
        }

        return {
            allowed: reasons.length === 0,
            reasons: reasons
        };
    }

    /**
     * إنتاج التوصية
     */
    generateRecommendation(confidence, direction, layerResults) {
        const recommendations = [];

        if (confidence >= 0.9) {
            recommendations.push('Very strong signal - consider higher position size');
        } else if (confidence >= 0.8) {
            recommendations.push('Strong signal - standard position size');
        } else if (confidence >= 0.7) {
            recommendations.push('Moderate signal - reduced position size');
        } else {
            recommendations.push('Weak signal - avoid trading');
        }

        // توصيات خاصة بكل طبقة
        if (layerResults.technical.valid && layerResults.technical.confidence > 0.8) {
            recommendations.push('Technical analysis strongly supports the signal');
        }

        if (layerResults.quantitative.valid && layerResults.quantitative.score > 0.8) {
            recommendations.push('Quantitative filters confirm favorable conditions');
        }

        if (layerResults.behavioral.valid && layerResults.behavioral.score > 0.7) {
            recommendations.push('Market behavior supports the direction');
        }

        if (layerResults.ai.valid && layerResults.ai.confidence > 0.8) {
            recommendations.push('AI model predicts high probability of success');
        }

        return recommendations;
    }

    /**
     * الحصول على تحليل كامل
     */
    getFullAnalysis() {
        return this.data.analysis;
    }

    /**
     * الحصول على إحصائيات الأداء
     */
    getPerformanceStats() {
        return this.performance;
    }
}

module.exports = HybridStrategy;
