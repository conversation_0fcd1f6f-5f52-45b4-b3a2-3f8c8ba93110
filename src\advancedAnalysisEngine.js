/**
 * محرك التحليل المتوازي المتقدم مع الاستراتيجية الرباعية
 * Advanced Parallel Analysis Engine with Quadruple Strategy
 * نسبة نجاح مستهدفة: 85%+
 */

const EventEmitter = require('events');
const TechnicalIndicators = require('./indicators');

class AdvancedAnalysisEngine extends EventEmitter {
    constructor(quotexConnector, options = {}) {
        super();
        
        this.quotexConnector = quotexConnector;
        this.options = {
            targetSuccessRate: options.targetSuccessRate || 85, // 85%+
            maxConcurrentAnalysis: options.maxConcurrentAnalysis || 20,
            analysisInterval: options.analysisInterval || 5000, // 5 ثوان
            minConfidenceLevel: options.minConfidenceLevel || 80, // 80%
            enableMachineLearning: options.enableMachineLearning !== false,
            enableQuantitativeAnalysis: options.enableQuantitativeAnalysis !== false,
            enableBehavioralAnalysis: options.enableBehavioralAnalysis !== false,
            enableTechnicalAnalysis: options.enableTechnicalAnalysis !== false,
            ...options
        };

        // محرك المؤشرات الفنية
        this.indicators = new TechnicalIndicators();
        
        // قائمة الأزواج المستهدفة
        this.targetPairs = [];
        
        // نتائج التحليل
        this.analysisResults = new Map(); // assetId -> analysis result
        
        // إحصائيات الأداء
        this.performanceStats = {
            totalAnalyses: 0,
            successfulSignals: 0,
            failedSignals: 0,
            averageConfidence: 0,
            successRate: 0,
            lastAnalysis: null
        };

        // مؤقت التحليل
        this.analysisTimer = null;
        
        this.isRunning = false;
    }

    /**
     * بدء محرك التحليل
     */
    async start() {
        try {
            console.log('🧠 Starting Advanced Analysis Engine...');
            
            // تحميل قائمة الأزواج
            this.targetPairs = this.quotexConnector.getTargetPairs();
            
            // بدء التحليل الدوري
            this.startPeriodicAnalysis();
            
            this.isRunning = true;
            console.log(`✅ Analysis Engine started for ${this.targetPairs.length} pairs`);
            this.emit('started');
            
            return true;
            
        } catch (error) {
            console.error('❌ Failed to start Analysis Engine:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * بدء التحليل الدوري
     */
    startPeriodicAnalysis() {
        this.analysisTimer = setInterval(async () => {
            try {
                await this.performParallelAnalysis();
            } catch (error) {
                console.error('❌ Error in periodic analysis:', error);
            }
        }, this.options.analysisInterval);
    }

    /**
     * تنفيذ التحليل المتوازي لجميع الأزواج
     */
    async performParallelAnalysis() {
        try {
            console.log('🔍 Performing parallel analysis...');
            
            // تقسيم الأزواج إلى مجموعات للمعالجة المتوازية
            const chunks = this.chunkArray(this.targetPairs, this.options.maxConcurrentAnalysis);
            
            for (const chunk of chunks) {
                // تحليل كل مجموعة بشكل متوازي
                const promises = chunk.map(pair => this.analyzePair(pair));
                await Promise.allSettled(promises);
            }
            
            this.performanceStats.lastAnalysis = new Date().toISOString();
            this.emit('analysisCompleted', this.getAnalysisResults());
            
        } catch (error) {
            console.error('❌ Error in parallel analysis:', error);
            this.emit('error', error);
        }
    }

    /**
     * تحليل زوج واحد باستخدام الاستراتيجية الرباعية
     */
    async analyzePair(pair) {
        try {
            // جلب البيانات المطلوبة للتحليل
            const candleData = await this.getCandleData(pair.id);
            if (!candleData || candleData.length < 50) {
                return null; // بيانات غير كافية
            }

            // الطبقة الأولى: التحليل الفني (Technical Analysis)
            const technicalAnalysis = await this.performTechnicalAnalysis(pair, candleData);
            
            // الطبقة الثانية: التحليل الكمي (Quantitative Analysis)
            const quantitativeAnalysis = await this.performQuantitativeAnalysis(pair, candleData);
            
            // الطبقة الثالثة: التحليل السلوكي (Behavioral Analysis)
            const behavioralAnalysis = await this.performBehavioralAnalysis(pair, candleData);
            
            // الطبقة الرابعة: الذكاء الاصطناعي (AI/ML Analysis)
            const aiAnalysis = await this.performAIAnalysis(pair, candleData);

            // دمج نتائج الطبقات الأربع
            const finalAnalysis = this.combineAnalysisLayers({
                technical: technicalAnalysis,
                quantitative: quantitativeAnalysis,
                behavioral: behavioralAnalysis,
                ai: aiAnalysis
            }, pair, candleData);

            // حفظ النتيجة
            this.analysisResults.set(pair.id, finalAnalysis);
            
            // إرسال إشارة إذا كانت قوية بما فيه الكفاية
            if (finalAnalysis.confidence >= this.options.minConfidenceLevel) {
                this.emit('signalGenerated', finalAnalysis);
            }

            this.performanceStats.totalAnalyses++;
            
            return finalAnalysis;
            
        } catch (error) {
            console.error(`❌ Error analyzing pair ${pair.symbol}:`, error);
            return null;
        }
    }

    /**
     * الطبقة الأولى: التحليل الفني
     */
    async performTechnicalAnalysis(pair, candleData) {
        try {
            const closes = candleData.map(c => c.close);
            const highs = candleData.map(c => c.high);
            const lows = candleData.map(c => c.low);
            const volumes = candleData.map(c => c.volume || 0);

            // حساب المؤشرات الفنية
            const ema5 = this.indicators.calculateEMA(closes, 5);
            const ema10 = this.indicators.calculateEMA(closes, 10);
            const ema21 = this.indicators.calculateEMA(closes, 21);
            const rsi5 = this.indicators.calculateRSI(closes, 5);
            const rsi14 = this.indicators.calculateRSI(closes, 14);
            const macd = this.indicators.calculateMACD(closes);
            const bollinger = this.indicators.calculateBollingerBands(closes, 20, 2);
            const momentum = this.indicators.calculateMomentum(closes, 10);
            const atr5 = this.indicators.calculateATR(highs, lows, closes, 5);
            const atr14 = this.indicators.calculateATR(highs, lows, closes, 14);

            // تحليل الإشارات الفنية
            const signals = this.analyzeTechnicalSignals({
                ema5, ema10, ema21, rsi5, rsi14, macd, bollinger, momentum, atr5, atr14
            }, closes);

            // تحديد مدة الصفقة المقترحة (1-5 دقائق)
            const suggestedDuration = this.calculateTechnicalDuration(signals, atr14);

            return {
                layer: 'technical',
                signals: signals,
                confidence: signals.overallConfidence,
                direction: signals.direction,
                strength: signals.strength,
                suggestedDuration: suggestedDuration,
                indicators: {
                    ema5: ema5[ema5.length - 1],
                    ema10: ema10[ema10.length - 1],
                    ema21: ema21[ema21.length - 1],
                    rsi5: rsi5[rsi5.length - 1],
                    rsi14: rsi14[rsi14.length - 1],
                    macd: macd[macd.length - 1],
                    bollinger: bollinger[bollinger.length - 1],
                    momentum: momentum[momentum.length - 1],
                    atr5: atr5[atr5.length - 1],
                    atr14: atr14[atr14.length - 1]
                }
            };
            
        } catch (error) {
            console.error('❌ Error in technical analysis:', error);
            return { layer: 'technical', confidence: 0, direction: 'neutral' };
        }
    }

    /**
     * الطبقة الثانية: التحليل الكمي
     */
    async performQuantitativeAnalysis(pair, candleData) {
        try {
            const closes = candleData.map(c => c.close);
            
            // حساب Z-Score للانحراف السعري
            const zScore = this.calculateZScore(closes);
            
            // تحليل الاحتمالات التاريخية
            const historicalProbability = await this.calculateHistoricalProbability(pair, candleData);
            
            // حساب نسبة شارب
            const sharpeRatio = this.calculateSharpeRatio(closes);
            
            // تحليل التقلبات
            const volatilityAnalysis = this.analyzeVolatility(closes);
            
            // تحليل الارتباط
            const correlationAnalysis = await this.analyzeCorrelation(pair, closes);

            // تحديد مدة الصفقة بناء على التحليل الكمي
            const suggestedDuration = this.calculateQuantitativeDuration(zScore, volatilityAnalysis);

            const confidence = this.calculateQuantitativeConfidence({
                zScore, historicalProbability, sharpeRatio, volatilityAnalysis, correlationAnalysis
            });

            return {
                layer: 'quantitative',
                confidence: confidence,
                direction: zScore > 2 ? 'put' : zScore < -2 ? 'call' : 'neutral',
                suggestedDuration: suggestedDuration,
                metrics: {
                    zScore: zScore,
                    historicalProbability: historicalProbability,
                    sharpeRatio: sharpeRatio,
                    volatility: volatilityAnalysis,
                    correlation: correlationAnalysis
                }
            };
            
        } catch (error) {
            console.error('❌ Error in quantitative analysis:', error);
            return { layer: 'quantitative', confidence: 0, direction: 'neutral' };
        }
    }

    /**
     * الطبقة الثالثة: التحليل السلوكي
     */
    async performBehavioralAnalysis(pair, candleData) {
        try {
            // تحليل أنماط الشموع
            const candlePatterns = this.analyzeCandlePatterns(candleData);
            
            // تحليل الزخم والتسارع
            const momentumAnalysis = this.analyzeMomentumBehavior(candleData);
            
            // تحليل السلوك اللحظي
            const instantBehavior = this.analyzeInstantBehavior(candleData);
            
            // تحليل الضغط العكسي
            const reversalPressure = this.analyzeReversalPressure(candleData);

            // تحديد مدة الصفقة بناء على السلوك
            const suggestedDuration = this.calculateBehavioralDuration(candlePatterns, momentumAnalysis);

            const confidence = this.calculateBehavioralConfidence({
                candlePatterns, momentumAnalysis, instantBehavior, reversalPressure
            });

            return {
                layer: 'behavioral',
                confidence: confidence,
                direction: this.determineBehavioralDirection(candlePatterns, momentumAnalysis),
                suggestedDuration: suggestedDuration,
                patterns: {
                    candles: candlePatterns,
                    momentum: momentumAnalysis,
                    instant: instantBehavior,
                    reversal: reversalPressure
                }
            };
            
        } catch (error) {
            console.error('❌ Error in behavioral analysis:', error);
            return { layer: 'behavioral', confidence: 0, direction: 'neutral' };
        }
    }

    /**
     * الطبقة الرابعة: الذكاء الاصطناعي
     */
    async performAIAnalysis(pair, candleData) {
        try {
            // تحضير البيانات للنموذج
            const features = this.prepareMLFeatures(candleData);
            
            // التنبؤ بالاتجاه
            const directionPrediction = await this.predictDirection(features);
            
            // تقييم درجة الثقة
            const confidenceScore = await this.calculateAIConfidence(features);
            
            // تصنيف حالة السوق
            const marketState = await this.classifyMarketState(features);
            
            // تحديد مدة الصفقة بناء على AI
            const suggestedDuration = this.calculateAIDuration(directionPrediction, marketState);

            return {
                layer: 'ai',
                confidence: confidenceScore,
                direction: directionPrediction.direction,
                probability: directionPrediction.probability,
                suggestedDuration: suggestedDuration,
                marketState: marketState,
                features: features
            };
            
        } catch (error) {
            console.error('❌ Error in AI analysis:', error);
            return { layer: 'ai', confidence: 0, direction: 'neutral' };
        }
    }

    /**
     * دمج نتائج الطبقات الأربع
     */
    combineAnalysisLayers(layers, pair, candleData) {
        try {
            const { technical, quantitative, behavioral, ai } = layers;
            
            // حساب الثقة الإجمالية (متوسط مرجح)
            const weights = {
                technical: 0.25,
                quantitative: 0.25,
                behavioral: 0.25,
                ai: 0.25
            };

            const overallConfidence = (
                technical.confidence * weights.technical +
                quantitative.confidence * weights.quantitative +
                behavioral.confidence * weights.behavioral +
                ai.confidence * weights.ai
            );

            // تحديد الاتجاه النهائي بناء على توافق الطبقات
            const directions = [technical.direction, quantitative.direction, behavioral.direction, ai.direction];
            const finalDirection = this.determineConsensusDirection(directions);

            // تحديد مدة الصفقة النهائية
            const durations = [
                technical.suggestedDuration,
                quantitative.suggestedDuration,
                behavioral.suggestedDuration,
                ai.suggestedDuration
            ].filter(d => d > 0);
            
            const finalDuration = durations.length > 0 ? 
                Math.round(durations.reduce((a, b) => a + b) / durations.length) : 300; // 5 دقائق افتراضي

            // حساب قوة الإشارة
            const signalStrength = this.calculateSignalStrength(layers, overallConfidence);

            return {
                assetId: pair.id,
                assetSymbol: pair.symbol,
                assetName: pair.name,
                timestamp: new Date().toISOString(),
                
                // النتيجة النهائية
                confidence: Math.round(overallConfidence * 100) / 100,
                direction: finalDirection,
                suggestedDuration: finalDuration,
                signalStrength: signalStrength,
                
                // تفاصيل الطبقات
                layers: layers,
                
                // معلومات إضافية
                currentPrice: candleData[candleData.length - 1].close,
                profitRate: this.quotexConnector.getProfitRate(pair.id) || 0.85,
                
                // توصية التداول
                recommendation: this.generateTradeRecommendation(overallConfidence, finalDirection, signalStrength)
            };
            
        } catch (error) {
            console.error('❌ Error combining analysis layers:', error);
            return {
                assetId: pair.id,
                confidence: 0,
                direction: 'neutral',
                recommendation: 'hold'
            };
        }
    }

    /**
     * تحليل الإشارات الفنية
     */
    analyzeTechnicalSignals(indicators, closes) {
        const signals = [];
        let bullishSignals = 0;
        let bearishSignals = 0;

        // تحليل تقاطع EMA
        const currentEMA5 = indicators.ema5[indicators.ema5.length - 1];
        const currentEMA10 = indicators.ema10[indicators.ema10.length - 1];
        const currentEMA21 = indicators.ema21[indicators.ema21.length - 1];

        if (currentEMA5 > currentEMA10 && currentEMA10 > currentEMA21) {
            signals.push({ type: 'ema_bullish', strength: 0.8 });
            bullishSignals++;
        } else if (currentEMA5 < currentEMA10 && currentEMA10 < currentEMA21) {
            signals.push({ type: 'ema_bearish', strength: 0.8 });
            bearishSignals++;
        }

        // تحليل RSI
        const currentRSI14 = indicators.rsi14[indicators.rsi14.length - 1];
        if (currentRSI14 < 30) {
            signals.push({ type: 'rsi_oversold', strength: 0.7 });
            bullishSignals++;
        } else if (currentRSI14 > 70) {
            signals.push({ type: 'rsi_overbought', strength: 0.7 });
            bearishSignals++;
        }

        // تحليل MACD
        const currentMACD = indicators.macd[indicators.macd.length - 1];
        if (currentMACD && currentMACD.histogram > 0) {
            signals.push({ type: 'macd_bullish', strength: 0.6 });
            bullishSignals++;
        } else if (currentMACD && currentMACD.histogram < 0) {
            signals.push({ type: 'macd_bearish', strength: 0.6 });
            bearishSignals++;
        }

        // حساب الثقة الإجمالية
        const totalSignals = bullishSignals + bearishSignals;
        const confidence = totalSignals > 0 ? Math.max(bullishSignals, bearishSignals) / totalSignals * 100 : 0;
        
        return {
            signals: signals,
            overallConfidence: confidence,
            direction: bullishSignals > bearishSignals ? 'call' : bearishSignals > bullishSignals ? 'put' : 'neutral',
            strength: Math.abs(bullishSignals - bearishSignals) / Math.max(totalSignals, 1)
        };
    }

    /**
     * حساب Z-Score
     */
    calculateZScore(prices) {
        const mean = prices.reduce((a, b) => a + b) / prices.length;
        const variance = prices.reduce((a, b) => a + Math.pow(b - mean, 2)) / prices.length;
        const stdDev = Math.sqrt(variance);
        const currentPrice = prices[prices.length - 1];
        
        return stdDev > 0 ? (currentPrice - mean) / stdDev : 0;
    }

    /**
     * تحديد مدة الصفقة للتحليل الفني
     */
    calculateTechnicalDuration(signals, atr) {
        const baseMinutes = 3; // 3 دقائق أساسي
        
        // تعديل بناء على قوة الإشارة
        const strengthMultiplier = signals.strength > 0.7 ? 1 : signals.strength > 0.5 ? 1.5 : 2;
        
        // تعديل بناء على التقلبات
        const volatilityMultiplier = atr > 0.001 ? 0.8 : 1.2;
        
        const duration = Math.round(baseMinutes * strengthMultiplier * volatilityMultiplier);
        return Math.max(1, Math.min(5, duration)) * 60; // تحويل لثواني، بحد أدنى دقيقة وأقصى 5 دقائق
    }

    /**
     * تقسيم المصفوفة
     */
    chunkArray(array, chunkSize) {
        const chunks = [];
        for (let i = 0; i < array.length; i += chunkSize) {
            chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
    }

    /**
     * جلب بيانات الشموع
     */
    async getCandleData(assetId) {
        try {
            // جلب الشموع الحية
            const liveCandles = this.quotexConnector.getLiveCandles(assetId, 50);
            
            // جلب الشموع التاريخية إذا لم تكن كافية
            if (liveCandles.length < 50) {
                const historicalCandles = await this.quotexConnector.getHistoricalCandles(assetId, 100);
                return [...historicalCandles, ...liveCandles].slice(-100);
            }
            
            return liveCandles;
            
        } catch (error) {
            console.error(`❌ Error getting candle data for asset ${assetId}:`, error);
            return [];
        }
    }

    /**
     * الحصول على نتائج التحليل
     */
    getAnalysisResults() {
        return Array.from(this.analysisResults.values());
    }

    /**
     * الحصول على إحصائيات الأداء
     */
    getPerformanceStats() {
        return {
            ...this.performanceStats,
            currentSuccessRate: this.performanceStats.totalAnalyses > 0 ? 
                (this.performanceStats.successfulSignals / this.performanceStats.totalAnalyses) * 100 : 0
        };
    }

    /**
     * إيقاف محرك التحليل
     */
    async stop() {
        try {
            console.log('🛑 Stopping Analysis Engine...');
            
            this.isRunning = false;
            
            if (this.analysisTimer) {
                clearInterval(this.analysisTimer);
                this.analysisTimer = null;
            }
            
            console.log('✅ Analysis Engine stopped');
            this.emit('stopped');
            
        } catch (error) {
            console.error('❌ Error stopping Analysis Engine:', error);
        }
    }

    /**
     * تحديد مدة الصفقة للتحليل الكمي
     */
    calculateQuantitativeDuration(zScore, volatilityAnalysis) {
        const baseMinutes = 3;

        // تعديل بناء على Z-Score
        const zScoreMultiplier = Math.abs(zScore) > 2 ? 0.8 : Math.abs(zScore) > 1 ? 1 : 1.5;

        // تعديل بناء على التقلبات
        const volatilityMultiplier = volatilityAnalysis.level === 'high' ? 0.7 :
                                   volatilityAnalysis.level === 'low' ? 1.5 : 1;

        const duration = Math.round(baseMinutes * zScoreMultiplier * volatilityMultiplier);
        return Math.max(1, Math.min(5, duration)) * 60;
    }

    /**
     * حساب الثقة للتحليل الكمي
     */
    calculateQuantitativeConfidence(metrics) {
        let confidence = 0;

        // Z-Score contribution
        if (Math.abs(metrics.zScore) > 2) confidence += 30;
        else if (Math.abs(metrics.zScore) > 1) confidence += 20;
        else confidence += 10;

        // Historical probability contribution
        confidence += metrics.historicalProbability * 25;

        // Sharpe ratio contribution
        if (metrics.sharpeRatio > 1) confidence += 20;
        else if (metrics.sharpeRatio > 0.5) confidence += 15;
        else confidence += 5;

        // Volatility contribution
        if (metrics.volatility.level === 'medium') confidence += 15;
        else confidence += 10;

        // Correlation contribution
        confidence += Math.abs(metrics.correlation) * 10;

        return Math.min(100, confidence);
    }

    /**
     * تحليل أنماط الشموع
     */
    analyzeCandlePatterns(candleData) {
        const patterns = [];
        const recentCandles = candleData.slice(-5); // آخر 5 شموع

        for (let i = 1; i < recentCandles.length; i++) {
            const current = recentCandles[i];
            const previous = recentCandles[i - 1];

            // Pin Bar pattern
            const bodySize = Math.abs(current.close - current.open);
            const upperShadow = current.high - Math.max(current.open, current.close);
            const lowerShadow = Math.min(current.open, current.close) - current.low;
            const totalRange = current.high - current.low;

            if (totalRange > 0) {
                if (upperShadow > bodySize * 2 && upperShadow > lowerShadow * 2) {
                    patterns.push({ type: 'pin_bar_bearish', strength: 0.8, candle: i });
                } else if (lowerShadow > bodySize * 2 && lowerShadow > upperShadow * 2) {
                    patterns.push({ type: 'pin_bar_bullish', strength: 0.8, candle: i });
                }
            }

            // Doji pattern
            if (bodySize < totalRange * 0.1) {
                patterns.push({ type: 'doji', strength: 0.6, candle: i });
            }

            // Engulfing pattern
            const prevBodySize = Math.abs(previous.close - previous.open);
            if (bodySize > prevBodySize * 1.5) {
                if (current.close > current.open && previous.close < previous.open) {
                    patterns.push({ type: 'bullish_engulfing', strength: 0.9, candle: i });
                } else if (current.close < current.open && previous.close > previous.open) {
                    patterns.push({ type: 'bearish_engulfing', strength: 0.9, candle: i });
                }
            }
        }

        return patterns;
    }

    /**
     * تحليل الزخم والتسارع
     */
    analyzeMomentumBehavior(candleData) {
        const closes = candleData.map(c => c.close);
        const momentum = [];

        for (let i = 1; i < closes.length; i++) {
            momentum.push(closes[i] - closes[i - 1]);
        }

        const recentMomentum = momentum.slice(-10);
        const avgMomentum = recentMomentum.reduce((a, b) => a + b, 0) / recentMomentum.length;

        // تحليل التسارع
        const acceleration = [];
        for (let i = 1; i < momentum.length; i++) {
            acceleration.push(momentum[i] - momentum[i - 1]);
        }

        const recentAcceleration = acceleration.slice(-5);
        const avgAcceleration = recentAcceleration.reduce((a, b) => a + b, 0) / recentAcceleration.length;

        return {
            momentum: avgMomentum,
            acceleration: avgAcceleration,
            direction: avgMomentum > 0 ? 'bullish' : 'bearish',
            strength: Math.abs(avgMomentum),
            accelerating: avgAcceleration > 0
        };
    }

    /**
     * تحديد مدة الصفقة للتحليل السلوكي
     */
    calculateBehavioralDuration(patterns, momentum) {
        const baseMinutes = 3;

        // تعديل بناء على قوة الأنماط
        const strongPatterns = patterns.filter(p => p.strength > 0.8).length;
        const patternMultiplier = strongPatterns > 0 ? 0.8 : 1.2;

        // تعديل بناء على الزخم
        const momentumMultiplier = momentum.accelerating ? 0.9 : 1.1;

        const duration = Math.round(baseMinutes * patternMultiplier * momentumMultiplier);
        return Math.max(1, Math.min(5, duration)) * 60;
    }

    /**
     * حساب الثقة للتحليل السلوكي
     */
    calculateBehavioralConfidence(analysis) {
        let confidence = 0;

        // أنماط الشموع
        const strongPatterns = analysis.candlePatterns.filter(p => p.strength > 0.8).length;
        confidence += strongPatterns * 25;

        // الزخم
        if (analysis.momentumAnalysis.accelerating) confidence += 20;
        confidence += Math.min(20, Math.abs(analysis.momentumAnalysis.momentum) * 1000);

        // السلوك اللحظي
        confidence += analysis.instantBehavior.consistency * 15;

        // الضغط العكسي
        if (analysis.reversalPressure.detected) confidence += 20;

        return Math.min(100, confidence);
    }

    /**
     * تحديد الاتجاه السلوكي
     */
    determineBehavioralDirection(patterns, momentum) {
        let bullishScore = 0;
        let bearishScore = 0;

        // تحليل الأنماط
        patterns.forEach(pattern => {
            if (pattern.type.includes('bullish')) {
                bullishScore += pattern.strength;
            } else if (pattern.type.includes('bearish')) {
                bearishScore += pattern.strength;
            }
        });

        // تحليل الزخم
        if (momentum.direction === 'bullish') {
            bullishScore += momentum.strength * 100;
        } else {
            bearishScore += momentum.strength * 100;
        }

        if (bullishScore > bearishScore) return 'call';
        if (bearishScore > bullishScore) return 'put';
        return 'neutral';
    }

    /**
     * تحضير ميزات التعلم الآلي
     */
    prepareMLFeatures(candleData) {
        const closes = candleData.map(c => c.close);
        const volumes = candleData.map(c => c.volume || 0);

        // ميزات السعر
        const priceFeatures = {
            currentPrice: closes[closes.length - 1],
            priceChange: closes[closes.length - 1] - closes[closes.length - 2],
            priceChangePercent: ((closes[closes.length - 1] - closes[closes.length - 2]) / closes[closes.length - 2]) * 100,
            volatility: this.calculateVolatility(closes.slice(-20)),
            trend: this.calculateTrend(closes.slice(-10))
        };

        // ميزات الحجم
        const volumeFeatures = {
            currentVolume: volumes[volumes.length - 1],
            avgVolume: volumes.slice(-10).reduce((a, b) => a + b, 0) / 10,
            volumeSpike: volumes[volumes.length - 1] > (volumes.slice(-10).reduce((a, b) => a + b, 0) / 10) * 1.5
        };

        // ميزات زمنية
        const timeFeatures = {
            hour: new Date().getHours(),
            dayOfWeek: new Date().getDay(),
            isMarketOpen: this.isMarketOpen()
        };

        return {
            ...priceFeatures,
            ...volumeFeatures,
            ...timeFeatures
        };
    }

    /**
     * التنبؤ بالاتجاه باستخدام AI
     */
    async predictDirection(features) {
        // محاكاة نموذج AI متقدم
        // في التطبيق الحقيقي، ستستخدم نموذج مدرب

        const score = (
            features.priceChangePercent * 0.3 +
            features.trend * 0.4 +
            (features.volumeSpike ? 20 : 0) * 0.2 +
            (features.isMarketOpen ? 10 : -5) * 0.1
        );

        const probability = Math.min(0.95, Math.max(0.55, 0.5 + Math.abs(score) / 100));

        return {
            direction: score > 0 ? 'call' : 'put',
            probability: probability,
            confidence: probability * 100
        };
    }

    /**
     * حساب ثقة AI
     */
    async calculateAIConfidence(features) {
        // محاكاة حساب الثقة المتقدم
        let confidence = 50; // أساسي

        // تعديل بناء على التقلبات
        if (features.volatility > 0.02) confidence += 20;
        else if (features.volatility < 0.005) confidence -= 10;

        // تعديل بناء على الاتجاه
        if (Math.abs(features.trend) > 0.5) confidence += 15;

        // تعديل بناء على الحجم
        if (features.volumeSpike) confidence += 10;

        // تعديل بناء على الوقت
        if (features.isMarketOpen) confidence += 5;

        return Math.min(95, Math.max(30, confidence));
    }

    /**
     * تصنيف حالة السوق
     */
    async classifyMarketState(features) {
        if (features.volatility > 0.03) return 'volatile';
        if (Math.abs(features.trend) > 0.7) return 'trending';
        if (features.volatility < 0.005) return 'ranging';
        return 'normal';
    }

    /**
     * تحديد مدة الصفقة للـ AI
     */
    calculateAIDuration(prediction, marketState) {
        const baseMinutes = 3;

        // تعديل بناء على الثقة
        const confidenceMultiplier = prediction.probability > 0.8 ? 0.8 :
                                    prediction.probability > 0.7 ? 1 : 1.3;

        // تعديل بناء على حالة السوق
        const stateMultiplier = marketState === 'volatile' ? 0.7 :
                               marketState === 'trending' ? 1.2 :
                               marketState === 'ranging' ? 1.5 : 1;

        const duration = Math.round(baseMinutes * confidenceMultiplier * stateMultiplier);
        return Math.max(1, Math.min(5, duration)) * 60;
    }

    /**
     * تحديد الاتجاه بالإجماع
     */
    determineConsensusDirection(directions) {
        const callCount = directions.filter(d => d === 'call').length;
        const putCount = directions.filter(d => d === 'put').length;

        if (callCount > putCount) return 'call';
        if (putCount > callCount) return 'put';
        return 'neutral';
    }

    /**
     * حساب قوة الإشارة
     */
    calculateSignalStrength(layers, confidence) {
        const agreementCount = Object.values(layers).filter(layer =>
            layer.direction !== 'neutral'
        ).length;

        const maxAgreement = 4; // عدد الطبقات
        const agreementRatio = agreementCount / maxAgreement;

        return (confidence / 100) * agreementRatio;
    }

    /**
     * توليد توصية التداول
     */
    generateTradeRecommendation(confidence, direction, strength) {
        if (confidence >= 85 && strength >= 0.7 && direction !== 'neutral') {
            return 'strong_' + direction;
        } else if (confidence >= 70 && strength >= 0.5 && direction !== 'neutral') {
            return 'moderate_' + direction;
        } else if (confidence >= 60 && direction !== 'neutral') {
            return 'weak_' + direction;
        } else {
            return 'hold';
        }
    }

    /**
     * دوال مساعدة إضافية
     */
    calculateVolatility(prices) {
        const returns = [];
        for (let i = 1; i < prices.length; i++) {
            returns.push((prices[i] - prices[i-1]) / prices[i-1]);
        }
        const mean = returns.reduce((a, b) => a + b, 0) / returns.length;
        const variance = returns.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / returns.length;
        return Math.sqrt(variance);
    }

    calculateTrend(prices) {
        const n = prices.length;
        const sumX = (n * (n - 1)) / 2;
        const sumY = prices.reduce((a, b) => a + b, 0);
        const sumXY = prices.reduce((sum, price, index) => sum + (index * price), 0);
        const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;

        const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        return slope;
    }

    isMarketOpen() {
        const now = new Date();
        const hour = now.getUTCHours();
        const day = now.getUTCDay();

        // تحقق مبسط من ساعات السوق (24/5 للفوركس)
        return day >= 1 && day <= 5; // الاثنين إلى الجمعة
    }

    analyzeInstantBehavior(candleData) {
        // تحليل السلوك اللحظي المبسط
        const recentCandles = candleData.slice(-3);
        const consistency = recentCandles.every(c => c.close > c.open) ? 1 :
                           recentCandles.every(c => c.close < c.open) ? 1 : 0.5;

        return { consistency };
    }

    analyzeReversalPressure(candleData) {
        // تحليل الضغط العكسي المبسط
        const lastCandle = candleData[candleData.length - 1];
        const bodySize = Math.abs(lastCandle.close - lastCandle.open);
        const totalRange = lastCandle.high - lastCandle.low;

        const detected = totalRange > 0 && (bodySize / totalRange) < 0.3;

        return { detected };
    }

    calculateHistoricalProbability(pair, candleData) {
        // محاكاة حساب الاحتمالية التاريخية
        return Math.random() * 0.4 + 0.6; // 60-100%
    }

    calculateSharpeRatio(prices) {
        const returns = [];
        for (let i = 1; i < prices.length; i++) {
            returns.push((prices[i] - prices[i-1]) / prices[i-1]);
        }

        const meanReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
        const variance = returns.reduce((a, b) => a + Math.pow(b - meanReturn, 2), 0) / returns.length;
        const stdDev = Math.sqrt(variance);

        return stdDev > 0 ? meanReturn / stdDev : 0;
    }

    analyzeVolatility(prices) {
        const volatility = this.calculateVolatility(prices);

        let level = 'medium';
        if (volatility > 0.03) level = 'high';
        else if (volatility < 0.01) level = 'low';

        return { value: volatility, level };
    }

    async analyzeCorrelation(pair, prices) {
        // محاكاة تحليل الارتباط
        return Math.random() * 0.6 - 0.3; // -0.3 إلى 0.3
    }
}

module.exports = AdvancedAnalysisEngine;
