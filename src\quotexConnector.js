/**
 * موصل منصة Quotex الحقيقي
 * Real Quotex Platform Connector
 */

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const WebSocket = require('ws');
const EventEmitter = require('events');
const SessionManager = require('./sessionManager');
const TradeManager = require('./tradeManager');
const CandleManager = require('./candleManager');

// استخدام plugin التخفي لتجنب الكشف
puppeteer.use(StealthPlugin());

class QuotexConnector extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            headless: options.headless !== false, // افتراضي true
            userDataDir: options.userDataDir || './user_data',
            timeout: options.timeout || 30000,
            ...options
        };
        
        this.browser = null;
        this.page = null;
        this.wsConnection = null;
        this.isConnected = false;
        this.isAuthenticated = false;
        
        // بيانات الحساب
        this.accountData = {
            balance: 0,
            isDemo: true,
            userId: null,
            sessionId: null
        };
        
        // بيانات الأدوات المالية
        this.instruments = new Map();
        this.profitRates = new Map();

        // قائمة الأزواج الـ70 المحددة
        this.targetPairs = this.initializeTargetPairs();

        // مدير الجلسات والكوكيز
        this.sessionManager = new SessionManager({
            sessionDir: './sessions',
            maxSessionAge: 24 * 60 * 60 * 1000 // 24 ساعة
        });

        // مدير الصفقات
        this.tradeManager = new TradeManager({
            tradesDir: './data/trades'
        });

        // مدير الشموع مع نظام FIFO
        this.candleManager = new CandleManager({
            dataDir: './data',
            maxLiveCandles: 100, // 100 شمعة حية لكل زوج
            maxHistoricalCandles: 10000 // 10000 شمعة تاريخية لكل زوج
        });

        // معالجة الرسائل
        this.messageHandlers = new Map();
        this.setupMessageHandlers();
    }

    /**
     * بدء الاتصال بمنصة Quotex - نظام جديد بدون فورم تسجيل دخول
     */
    async connect() {
        try {
            console.log('🚀 Starting Quotex connection with automatic login...');

            // تهيئة مدير الجلسات
            await this.sessionManager.initialize();

            // تهيئة مدير الصفقات
            await this.tradeManager.initialize();

            // تهيئة مدير الشموع
            await this.candleManager.initialize();

            // التحقق من وجود جلسة صالحة
            const existingSession = this.sessionManager.getCurrentSession();
            if (existingSession && existingSession.isValid) {
                console.log('🔓 Found valid session, attempting to restore...');
                const restored = await this.restoreSession(existingSession);
                if (restored) {
                    return true;
                }
            }

            // إطلاق المتصفح مع إعدادات محسنة
            this.browser = await puppeteer.launch({
                headless: false, // دائماً مرئي للمستخدم لتسجيل الدخول
                userDataDir: this.options.userDataDir,
                defaultViewport: null,
                args: [
                    '--start-maximized',
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            });

            this.page = await this.browser.newPage();

            // تعيين User Agent محدث
            await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

            // تفعيل اعتراض الطلبات لمراقبة WebSocket
            await this.page.setRequestInterception(true);

            // مراقبة طلبات WebSocket والكوكيز
            this.page.on('request', request => {
                const url = request.url();
                if (url.includes('socket.io') || url.includes('ws')) {
                    console.log('🔌 WebSocket request detected:', url);
                }
                request.continue();
            });

            // مراقبة الاستجابات للحصول على SSID
            this.page.on('response', async response => {
                if (response.url().includes('socket.io') || response.url().includes('authorization')) {
                    try {
                        const headers = response.headers();
                        console.log('📡 Response headers:', headers);
                    } catch (error) {
                        // تجاهل الأخطاء
                    }
                }
            });

            // الانتقال لصفحة تسجيل الدخول
            console.log('📱 Opening Quotex login page for manual authentication...');
            await this.page.goto('https://qxbroker.com/en/sign-in', {
                waitUntil: 'networkidle2',
                timeout: 60000
            });

            console.log('👤 Please login manually in the browser window...');
            console.log('⏳ Waiting for successful authentication...');

            // انتظار تسجيل الدخول اليدوي
            await this.waitForManualLogin();

            // إعداد اتصال WebSocket
            await this.setupWebSocket();

            // جلب بيانات الحساب والأدوات
            await this.loadAccountData();
            await this.loadInstruments();

            // حفظ الجلسة والكوكيز
            await this.saveCurrentSession();

            this.isConnected = true;
            this.emit('connected');

            console.log('✅ Successfully connected to Quotex platform');
            return true;

        } catch (error) {
            console.error('❌ Failed to connect to Quotex:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * انتظار تسجيل الدخول اليدوي
     */
    async waitForManualLogin() {
        try {
            console.log('⏳ Waiting for manual login...');

            // انتظار حتى يتم الانتقال لصفحة التداول أو لوحة التحكم
            await Promise.race([
                // انتظار الانتقال لصفحة التداول
                this.page.waitForURL('**/trade**', { timeout: 300000 }), // 5 دقائق
                this.page.waitForURL('**/demo-trade**', { timeout: 300000 }),
                this.page.waitForURL('**/platform**', { timeout: 300000 }),
                // أو انتظار ظهور عناصر منصة التداول
                this.page.waitForSelector('.trading-view, .chart-container, .asset-select', { timeout: 300000 })
            ]);

            console.log('✅ Manual login detected, proceeding...');

            // انتظار إضافي لتحميل المنصة بالكامل
            await this.page.waitForTimeout(5000);

            // التحقق من وجود عناصر المنصة
            const platformLoaded = await this.page.evaluate(() => {
                return !!(
                    document.querySelector('.trading-view') ||
                    document.querySelector('.chart-container') ||
                    document.querySelector('.asset-select') ||
                    window.WebSocket
                );
            });

            if (!platformLoaded) {
                throw new Error('Trading platform not loaded after login');
            }

            console.log('✅ Trading platform loaded successfully');

        } catch (error) {
            console.error('❌ Manual login timeout or failed:', error);
            throw new Error('Manual login failed: ' + error.message);
        }
    }

    /**
     * انتظار تحميل منصة التداول
     */
    async waitForPlatform() {
        try {
            console.log('⏳ Waiting for trading platform to load...');
            
            // انتظار عناصر المنصة الأساسية
            await this.page.waitForSelector('.trading-view', { timeout: 30000 });
            await this.page.waitForSelector('.asset-select', { timeout: 30000 });
            
            // انتظار تحميل البيانات
            await this.page.waitForFunction(() => {
                return window.WebSocket && window.WebSocket.prototype;
            }, { timeout: 30000 });
            
            console.log('✅ Trading platform loaded');
            
        } catch (error) {
            console.error('❌ Platform loading failed:', error);
            throw new Error('Platform loading failed: ' + error.message);
        }
    }

    /**
     * إعداد اتصال WebSocket
     */
    async setupWebSocket() {
        try {
            console.log('🔌 Setting up WebSocket connection...');
            
            // الحصول على معلومات WebSocket من الصفحة
            const wsInfo = await this.page.evaluate(() => {
                // البحث عن اتصال WebSocket في الصفحة
                if (window.wsConnection) {
                    return {
                        url: window.wsConnection.url,
                        sessionId: window.sessionId || null
                    };
                }
                return null;
            });

            if (!wsInfo) {
                // إنشاء اتصال WebSocket جديد
                const wsUrl = 'wss://ws.qxbroker.com/socket.io/?EIO=3&transport=websocket';
                this.wsConnection = new WebSocket(wsUrl);
                
                this.wsConnection.on('open', () => {
                    console.log('✅ WebSocket connected');
                    this.emit('wsConnected');
                });

                this.wsConnection.on('message', (data) => {
                    this.handleWebSocketMessage(data.toString());
                });

                this.wsConnection.on('error', (error) => {
                    console.error('❌ WebSocket error:', error);
                    this.emit('wsError', error);
                });

                this.wsConnection.on('close', () => {
                    console.log('🔌 WebSocket disconnected');
                    this.emit('wsDisconnected');
                });
            }
            
        } catch (error) {
            console.error('❌ WebSocket setup failed:', error);
            throw error;
        }
    }

    /**
     * معالجة رسائل WebSocket
     */
    handleWebSocketMessage(message) {
        try {
            // تحليل رسائل Socket.IO
            if (message === '0') {
                console.log('🔗 WebSocket handshake completed');
                return;
            }
            
            if (message === '40') {
                console.log('✅ WebSocket authenticated');
                this.isAuthenticated = true;
                this.emit('authenticated');
                return;
            }
            
            if (message.startsWith('42')) {
                const data = JSON.parse(message.substring(2));
                const event = data[0];
                const payload = data[1];
                
                // توجيه الرسالة للمعالج المناسب
                if (this.messageHandlers.has(event)) {
                    this.messageHandlers.get(event)(payload);
                }
                
                this.emit('message', { event, payload });
            }
            
        } catch (error) {
            console.error('❌ Error handling WebSocket message:', error);
        }
    }

    /**
     * إعداد معالجات الرسائل
     */
    setupMessageHandlers() {
        // معالج بيانات الأسعار المباشرة
        this.messageHandlers.set('price', (data) => {
            this.emit('priceUpdate', data);
        });

        // معالج بيانات الشموع
        this.messageHandlers.set('candle', async (data) => {
            try {
                // الحصول على معلومات الأصل
                const assetInfo = this.instruments.get(data.assetId) || {
                    name: this.getAssetName(data.assetId),
                    symbol: data.assetId
                };

                // إضافة أو تحديث الشمعة في مدير الشموع
                if (data.isNewCandle) {
                    // شمعة جديدة - إغلاق السابقة وإضافة الجديدة
                    await this.candleManager.closeCurrentCandle(data.assetId, {
                        close: data.previousClose,
                        volume: data.previousVolume
                    });

                    await this.candleManager.addCandle(data.assetId, {
                        timestamp: data.timestamp,
                        open: data.open,
                        high: data.high,
                        low: data.low,
                        close: data.close,
                        volume: data.volume || 0,
                        timeframe: data.timeframe || 60,
                        isComplete: false
                    }, assetInfo);
                } else {
                    // تحديث الشمعة الحالية
                    await this.candleManager.updateCurrentCandle(data.assetId, {
                        high: data.high,
                        low: data.low,
                        close: data.close,
                        volume: data.volume || 0,
                        isComplete: data.isComplete || false
                    });
                }

                this.emit('candleUpdate', data);

            } catch (error) {
                console.error('❌ Error processing candle data:', error);
                this.emit('candleUpdate', data);
            }
        });

        // معالج رصيد الحساب
        this.messageHandlers.set('balance', (data) => {
            this.accountData.balance = data.balance;
            this.emit('balanceUpdate', data);

            // تحديث الجلسة مع الرصيد الجديد
            this.sessionManager.updateSessionActivity({
                balance: data.balance
            }).catch(console.error);
        });

        // معالج البيانات التاريخية
        this.messageHandlers.set('chart_notification', (data) => {
            if (data && data.candles) {
                console.log(`📊 Received ${data.candles.length} historical candles`);
                this.emit('historicalDataReceived', {
                    requestId: data.requestId,
                    asset: data.asset,
                    candles: data.candles.map(candle => ({
                        timestamp: new Date(candle.time * 1000),
                        open: parseFloat(candle.open),
                        high: parseFloat(candle.high),
                        low: parseFloat(candle.low),
                        close: parseFloat(candle.close),
                        volume: parseFloat(candle.volume || 0)
                    }))
                });
            }
        });

        // معالج قائمة الأدوات المالية
        this.messageHandlers.set('instruments', (data) => {
            if (data && Array.isArray(data)) {
                console.log(`📋 Received ${data.length} instruments`);
                data.forEach(instrument => {
                    this.instruments.set(instrument.symbol, {
                        id: instrument.symbol,
                        name: instrument.name,
                        type: instrument.type,
                        status: instrument.status,
                        payout: instrument.payout,
                        change24: instrument.change24
                    });

                    // تحديث نسب الأرباح
                    if (instrument.payout) {
                        this.profitRates.set(instrument.symbol, instrument.payout);
                    }
                });
                this.emit('instrumentsUpdated', data);
            }
        });

        // معالج نسب الأرباح
        this.messageHandlers.set('profit_rates', (data) => {
            if (data) {
                Object.entries(data).forEach(([symbol, rate]) => {
                    this.profitRates.set(symbol, rate);
                });
                this.emit('profitRatesUpdated', data);
            }
        });

        // معالج نسب الأرباح
        this.messageHandlers.set('profit_rates', (data) => {
            if (data && data.rates) {
                for (const [assetId, rate] of Object.entries(data.rates)) {
                    this.profitRates.set(parseInt(assetId), rate);
                }
                this.emit('profitRatesUpdate', data);
            }
        });

        // معالج نتائج الصفقات
        this.messageHandlers.set('trade_result', async (data) => {
            try {
                // البحث عن الصفقة في مدير الصفقات
                const openTrades = this.tradeManager.getOpenTrades();
                const matchingTrade = openTrades.find(trade =>
                    trade.assetId === data.assetId &&
                    Math.abs(new Date(trade.openTime).getTime() - data.openTime) < 60000 // خلال دقيقة
                );

                if (matchingTrade) {
                    // إغلاق الصفقة في مدير الصفقات
                    await this.tradeManager.closeTrade(matchingTrade.id, {
                        closePrice: data.closePrice,
                        closeTime: data.closeTime || new Date().toISOString(),
                        result: data.result // 'win', 'loss', 'tie'
                    });

                    console.log(`🏁 Trade result processed: ${matchingTrade.id} - ${data.result}`);
                }

                this.emit('tradeResult', data);

            } catch (error) {
                console.error('❌ Error processing trade result:', error);
                this.emit('tradeResult', data);
            }
        });
    }

    /**
     * جلب بيانات الحساب
     */
    async loadAccountData() {
        try {
            console.log('📊 Loading account data...');
            
            const accountInfo = await this.page.evaluate(() => {
                // محاولة الحصول على بيانات الحساب من الصفحة
                if (window.accountData) {
                    return window.accountData;
                }
                
                // البحث في DOM
                const balanceElement = document.querySelector('.balance-amount');
                const demoToggle = document.querySelector('.demo-toggle');
                
                return {
                    balance: balanceElement ? parseFloat(balanceElement.textContent) : 0,
                    isDemo: demoToggle ? demoToggle.classList.contains('active') : true
                };
            });

            Object.assign(this.accountData, accountInfo);
            console.log('✅ Account data loaded:', this.accountData);
            
        } catch (error) {
            console.error('❌ Failed to load account data:', error);
        }
    }

    /**
     * جلب قائمة الأدوات المالية باستخدام WebSocket
     */
    async loadInstruments() {
        try {
            console.log('📋 Loading instruments list via WebSocket...');

            if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
                console.log('⚠️ WebSocket not available, trying DOM method...');
                return await this.loadInstrumentsFromDOM();
            }

            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Instruments loading timeout'));
                }, 15000);

                const handleInstruments = (data) => {
                    clearTimeout(timeout);
                    this.removeListener('instrumentsUpdated', handleInstruments);
                    console.log(`✅ Loaded ${this.instruments.size} instruments via WebSocket`);
                    resolve(Array.from(this.instruments.values()));
                };

                this.on('instrumentsUpdated', handleInstruments);

                // طلب قائمة الأدوات
                this.sendWebSocketMessage('instruments/list', {});
            });

        } catch (error) {
            console.error('❌ Failed to load instruments via WebSocket:', error);
            // محاولة بديلة من DOM
            return await this.loadInstrumentsFromDOM();
        }
    }

    /**
     * جلب قائمة الأدوات من DOM كبديل
     */
    async loadInstrumentsFromDOM() {
        try {
            console.log('📋 Loading instruments from DOM...');

            const instruments = await this.page.evaluate(() => {
                // محاولة الحصول على قائمة الأدوات من الصفحة
                if (window.instrumentsList) {
                    return window.instrumentsList;
                }

                // البحث في DOM
                const assetElements = document.querySelectorAll('.asset-item, [data-asset], .instrument-item');
                const instruments = [];

                assetElements.forEach(element => {
                    const id = element.getAttribute('data-asset-id') ||
                              element.getAttribute('data-asset') ||
                              element.getAttribute('data-symbol');
                    const name = element.querySelector('.asset-name, .instrument-name')?.textContent ||
                                element.textContent;
                    const isActive = !element.classList.contains('disabled');

                    if (id && name) {
                        instruments.push({
                            id: id,
                            symbol: id,
                            name: name.trim(),
                            isActive: isActive,
                            type: 'currency'
                        });
                    }
                });

                return instruments;
            });

            // تخزين الأدوات
            instruments.forEach(instrument => {
                this.instruments.set(instrument.symbol || instrument.id, instrument);
            });

            console.log(`✅ Loaded ${instruments.length} instruments from DOM`);
            this.emit('instrumentsLoaded', instruments);
            return instruments;

        } catch (error) {
            console.error('❌ Failed to load instruments from DOM:', error);
            return [];
        }
    }

    /**
     * تنفيذ صفقة
     */
    async placeTrade(assetId, amount, direction, duration = 300, additionalData = {}) {
        try {
            if (!this.isConnected || !this.isAuthenticated) {
                throw new Error('Not connected or authenticated');
            }

            console.log(`📈 Placing ${direction} trade for asset ${assetId}, amount: $${amount}, duration: ${duration}s`);

            // الحصول على السعر الحالي
            const currentPrice = await this.getCurrentPrice(assetId);
            const assetName = this.getAssetName(assetId);
            const profitRate = this.profitRates.get(assetId) || 0.85;

            // إنشاء سجل الصفقة في مدير الصفقات
            const tradeData = {
                assetId: assetId,
                assetName: assetName,
                direction: direction,
                amount: amount,
                duration: duration,
                openPrice: currentPrice,
                profitRate: profitRate,
                analysis: additionalData.analysis,
                confidence: additionalData.confidence,
                strategy: additionalData.strategy || 'hybrid',
                sessionId: this.sessionManager.getCurrentSession()?.id,
                userId: this.accountData.userId,
                accountType: this.accountData.isDemo ? 'demo' : 'real'
            };

            const trade = await this.tradeManager.createTrade(tradeData);

            // تنفيذ الصفقة عبر الصفحة
            const result = await this.page.evaluate(async (assetId, amount, direction, duration) => {
                // اختيار الأصل
                const assetSelector = document.querySelector(`[data-asset-id="${assetId}"]`);
                if (assetSelector) {
                    assetSelector.click();
                    await new Promise(resolve => setTimeout(resolve, 500));
                }

                // تعيين المبلغ
                const amountInput = document.querySelector('.amount-input');
                if (amountInput) {
                    amountInput.value = amount;
                    amountInput.dispatchEvent(new Event('input', { bubbles: true }));
                }

                // تعيين المدة
                const durationSelector = document.querySelector(`[data-duration="${duration}"]`);
                if (durationSelector) {
                    durationSelector.click();
                    await new Promise(resolve => setTimeout(resolve, 300));
                }

                // تنفيذ الصفقة
                const tradeButton = document.querySelector(direction === 'call' ? '.btn-call' : '.btn-put');
                if (tradeButton && !tradeButton.disabled) {
                    tradeButton.click();
                    return {
                        success: true,
                        timestamp: Date.now(),
                        assetId: assetId,
                        amount: amount,
                        direction: direction,
                        duration: duration
                    };
                }

                return { success: false, reason: 'Trade button not available' };
            }, assetId, amount, direction, duration);

            if (result.success) {
                // تحديث سجل الصفقة بمعرف المنصة إذا توفر
                await this.tradeManager.updateTrade(trade.id, {
                    platformTradeId: result.platformTradeId,
                    executionTime: new Date(result.timestamp).toISOString()
                });

                console.log('✅ Trade placed successfully');
                this.emit('tradePlaced', { ...result, tradeRecord: trade });

                return { ...result, tradeRecord: trade };
            } else {
                // حذف سجل الصفقة إذا فشل التنفيذ
                await this.tradeManager.updateTrade(trade.id, {
                    status: 'failed',
                    failureReason: result.reason
                });

                console.log('❌ Trade placement failed:', result.reason);
                return result;
            }

        } catch (error) {
            console.error('❌ Error placing trade:', error);
            throw error;
        }
    }

    /**
     * جلب البيانات التاريخية باستخدام WebSocket
     */
    async getHistoricalData(assetId, timeframe = 60, count = 500) {
        try {
            console.log(`📊 Fetching ${count} historical candles for asset ${assetId}`);

            if (!this.wsConnection || this.wsConnection.readyState !== WebSocket.OPEN) {
                throw new Error('WebSocket connection not available');
            }

            // تحويل timeframe إلى تنسيق Quotex
            const interval = this.convertTimeframeToInterval(timeframe);

            return new Promise((resolve, reject) => {
                const requestId = Date.now();
                const timeout = setTimeout(() => {
                    reject(new Error('Historical data request timeout'));
                }, 30000);

                // معالج استقبال البيانات التاريخية
                const handleHistoricalData = (data) => {
                    if (data.requestId === requestId) {
                        clearTimeout(timeout);
                        this.removeListener('historicalDataReceived', handleHistoricalData);

                        if (data.candles && data.candles.length > 0) {
                            console.log(`✅ Retrieved ${data.candles.length} historical candles for ${assetId}`);
                            resolve(data.candles);
                        } else {
                            console.log(`⚠️ No historical data available for ${assetId}`);
                            resolve([]);
                        }
                    }
                };

                this.on('historicalDataReceived', handleHistoricalData);

                // إرسال طلب البيانات التاريخية
                const request = {
                    asset: assetId,
                    interval: interval,
                    offset: 0,
                    count: count,
                    requestId: requestId
                };

                this.sendWebSocketMessage('chart_notification/get', request);
            });

        } catch (error) {
            console.error(`❌ Error fetching historical data for ${assetId}:`, error);
            return [];
        }
    }

    /**
     * تحويل timeframe إلى interval format
     */
    convertTimeframeToInterval(timeframe) {
        const timeframeMap = {
            60: 'M1',    // 1 دقيقة
            300: 'M5',   // 5 دقائق
            900: 'M15',  // 15 دقيقة
            1800: 'M30', // 30 دقيقة
            3600: 'H1',  // 1 ساعة
            14400: 'H4', // 4 ساعات
            86400: 'D1'  // 1 يوم
        };

        return timeframeMap[timeframe] || 'M1';
    }

    /**
     * إرسال رسالة عبر WebSocket
     */
    sendWebSocketMessage(event, data) {
        if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
            const message = `42${JSON.stringify([event, data])}`;
            this.wsConnection.send(message);
            console.log(`📤 Sent WebSocket message: ${event}`);
        } else {
            console.error('❌ WebSocket not connected');
        }
    }

    /**
     * جلب البيانات التاريخية لجميع الأزواج الـ70
     */
    async getAllHistoricalData(timeframe = 60, count = 500) {
        try {
            console.log(`📊 Fetching historical data for all ${this.targetPairs.length} target pairs...`);

            const results = new Map();
            const batchSize = 5; // معالجة 5 أزواج في كل مرة لتجنب الحمل الزائد

            for (let i = 0; i < this.targetPairs.length; i += batchSize) {
                const batch = this.targetPairs.slice(i, i + batchSize);

                console.log(`📈 Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(this.targetPairs.length/batchSize)}`);

                const batchPromises = batch.map(async (pair) => {
                    try {
                        const data = await this.getHistoricalData(pair.symbol, timeframe, count);
                        if (data && data.length > 0) {
                            results.set(pair.symbol, {
                                symbol: pair.symbol,
                                name: pair.name,
                                category: pair.category,
                                isOTC: pair.isOTC,
                                data: data,
                                count: data.length,
                                lastUpdate: new Date()
                            });
                            console.log(`✅ ${pair.symbol}: ${data.length} candles`);
                        } else {
                            console.log(`⚠️ ${pair.symbol}: No data available`);
                        }
                    } catch (error) {
                        console.error(`❌ ${pair.symbol}: ${error.message}`);
                    }
                });

                await Promise.all(batchPromises);

                // تأخير بين المجموعات
                if (i + batchSize < this.targetPairs.length) {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            }

            console.log(`✅ Historical data collection completed: ${results.size}/${this.targetPairs.length} pairs`);
            this.emit('allHistoricalDataReceived', results);

            return results;

        } catch (error) {
            console.error('❌ Error fetching all historical data:', error);
            return new Map();
        }
    }

    /**
     * الاشتراك في البيانات المباشرة لجميع الأزواج
     */
    async subscribeToAllPairs() {
        try {
            console.log(`📡 Subscribing to live data for all ${this.targetPairs.length} target pairs...`);

            const subscriptions = [];

            for (const pair of this.targetPairs) {
                try {
                    // الاشتراك في البيانات المباشرة
                    this.sendWebSocketMessage('instruments/follow', { asset: pair.symbol });
                    subscriptions.push(pair.symbol);

                    // تأخير صغير بين الاشتراكات
                    await new Promise(resolve => setTimeout(resolve, 100));

                } catch (error) {
                    console.error(`❌ Failed to subscribe to ${pair.symbol}:`, error);
                }
            }

            console.log(`✅ Subscribed to ${subscriptions.length} pairs for live data`);
            this.emit('allPairsSubscribed', subscriptions);

            return subscriptions;

        } catch (error) {
            console.error('❌ Error subscribing to all pairs:', error);
            return [];
        }
    }

    /**
     * الحصول على نسب الأرباح
     */
    getProfitRates() {
        return Object.fromEntries(this.profitRates);
    }

    /**
     * الحصول على رصيد الحساب
     */
    getAccountBalance() {
        return this.accountData;
    }

    /**
     * الحصول على قائمة الأدوات
     */
    getInstruments() {
        return Array.from(this.instruments.values());
    }

    /**
     * تهيئة قائمة الأزواج الـ70 المستهدفة
     */
    initializeTargetPairs() {
        return [
            // الأزواج الرئيسية والثانوية
            { symbol: 'GBPUSD', name: 'GBP/USD', category: 'major', isOTC: false },
            { symbol: 'GBPUSD_otc', name: 'GBP/USD OTC', category: 'major', isOTC: true },
            { symbol: 'USDJPY', name: 'USD/JPY', category: 'major', isOTC: false },
            { symbol: 'USDJPY_otc', name: 'USD/JPY OTC', category: 'major', isOTC: true },
            { symbol: 'CHFJPY', name: 'CHF/JPY', category: 'minor', isOTC: false },
            { symbol: 'CHFJPY_otc', name: 'CHF/JPY OTC', category: 'minor', isOTC: true },
            { symbol: 'USDCAD', name: 'USD/CAD', category: 'major', isOTC: false },
            { symbol: 'USDCAD_otc', name: 'USD/CAD OTC', category: 'major', isOTC: true },
            { symbol: 'AUDCAD', name: 'AUD/CAD', category: 'minor', isOTC: false },
            { symbol: 'AUDCAD_otc', name: 'AUD/CAD OTC', category: 'minor', isOTC: true },
            { symbol: 'USDCHF', name: 'USD/CHF', category: 'major', isOTC: false },
            { symbol: 'USDCHF_otc', name: 'USD/CHF OTC', category: 'major', isOTC: true },
            { symbol: 'EURGBP', name: 'EUR/GBP', category: 'major', isOTC: false },
            { symbol: 'EURGBP_otc', name: 'EUR/GBP OTC', category: 'major', isOTC: true },
            { symbol: 'EURAUD', name: 'EUR/AUD', category: 'minor', isOTC: false },
            { symbol: 'EURCAD', name: 'EUR/CAD', category: 'minor', isOTC: false },
            { symbol: 'AUDUSD', name: 'AUD/USD', category: 'major', isOTC: false },
            { symbol: 'AUDUSD_otc', name: 'AUD/USD OTC', category: 'major', isOTC: true },
            { symbol: 'CADCHF', name: 'CAD/CHF', category: 'minor', isOTC: false },
            { symbol: 'CADCHF_otc', name: 'CAD/CHF OTC', category: 'minor', isOTC: true },
            { symbol: 'EURJPY', name: 'EUR/JPY', category: 'major', isOTC: false },
            { symbol: 'EURJPY_otc', name: 'EUR/JPY OTC', category: 'major', isOTC: true },
            { symbol: 'AUDCHF', name: 'AUD/CHF', category: 'minor', isOTC: false },
            { symbol: 'GBPCHF', name: 'GBP/CHF', category: 'minor', isOTC: false },
            { symbol: 'AUDJPY', name: 'AUD/JPY', category: 'minor', isOTC: false },
            { symbol: 'AUDJPY_otc', name: 'AUD/JPY OTC', category: 'minor', isOTC: true },
            { symbol: 'GBPJPY', name: 'GBP/JPY', category: 'minor', isOTC: false },
            { symbol: 'GBPJPY_otc', name: 'GBP/JPY OTC', category: 'minor', isOTC: true },
            { symbol: 'GBPAUD', name: 'GBP/AUD', category: 'minor', isOTC: false },
            { symbol: 'GBPAUD_otc', name: 'GBP/AUD OTC', category: 'minor', isOTC: true },
            { symbol: 'GBPCAD', name: 'GBP/CAD', category: 'minor', isOTC: false },
            { symbol: 'CADJPY', name: 'CAD/JPY', category: 'minor', isOTC: false },
            { symbol: 'CADJPY_otc', name: 'CAD/JPY OTC', category: 'minor', isOTC: true },
            { symbol: 'EURCHF', name: 'EUR/CHF', category: 'major', isOTC: false },
            { symbol: 'EURCHF_otc', name: 'EUR/CHF OTC', category: 'major', isOTC: true },
            { symbol: 'EURUSD', name: 'EUR/USD', category: 'major', isOTC: false },
            { symbol: 'EURUSD_otc', name: 'EUR/USD OTC', category: 'major', isOTC: true },

            // الأزواج الغريبة والناشئة
            { symbol: 'USDPHP_otc', name: 'USD/PHP OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDSGD_otc', name: 'USD/SGD OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDVND_otc', name: 'USD/VND OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDMYR_otc', name: 'USD/MYR OTC', category: 'exotic', isOTC: true },
            { symbol: 'NGNUSD_otc', name: 'NGN/USD OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDRUB_otc', name: 'USD/RUB OTC', category: 'exotic', isOTC: true },
            { symbol: 'TNDUSD_otc', name: 'TND/USD OTC', category: 'exotic', isOTC: true },
            { symbol: 'NZDJPY_otc', name: 'NZD/JPY OTC', category: 'minor', isOTC: true },
            { symbol: 'USDTHB_otc', name: 'USD/THB OTC', category: 'exotic', isOTC: true },
            { symbol: 'LBPUSD_otc', name: 'LBP/USD OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDBRL_otc', name: 'USD/BRL OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDPKR_otc', name: 'USD/PKR OTC', category: 'exotic', isOTC: true },
            { symbol: 'EURNZD_otc', name: 'EUR/NZD OTC', category: 'minor', isOTC: true },
            { symbol: 'USDDZD_otc', name: 'USD/DZD OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDEGP_otc', name: 'USD/EGP OTC', category: 'exotic', isOTC: true },
            { symbol: 'NZDUSD_otc', name: 'NZD/USD OTC', category: 'major', isOTC: true },
            { symbol: 'AUDNZD_otc', name: 'AUD/NZD OTC', category: 'minor', isOTC: true },
            { symbol: 'YERUSD_otc', name: 'YER/USD OTC', category: 'exotic', isOTC: true },
            { symbol: 'EURHUF_otc', name: 'EUR/HUF OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDMXN_otc', name: 'USD/MXN OTC', category: 'exotic', isOTC: true },
            { symbol: 'IRRUSD_otc', name: 'IRR/USD OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDBDT_otc', name: 'USD/BDT OTC', category: 'exotic', isOTC: true },
            { symbol: 'EURTRY_otc', name: 'EUR/TRY OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDIDR_otc', name: 'USD/IDR OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDINR_otc', name: 'USD/INR OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDCLP_otc', name: 'USD/CLP OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDCNH_otc', name: 'USD/CNH OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDCOP_otc', name: 'USD/COP OTC', category: 'exotic', isOTC: true },
            { symbol: 'ZARUSD_otc', name: 'ZAR/USD OTC', category: 'exotic', isOTC: true },
            { symbol: 'USDARS_otc', name: 'USD/ARS OTC', category: 'exotic', isOTC: true },
            { symbol: 'EURRUB_otc', name: 'EUR/RUB OTC', category: 'exotic', isOTC: true },
            { symbol: 'CHFNOK_otc', name: 'CHF/NOK OTC', category: 'exotic', isOTC: true }
        ];
    }

    /**
     * الحصول على قائمة الأزواج المستهدفة
     */
    getTargetPairs() {
        return this.targetPairs;
    }

    /**
     * البحث عن زوج بالرمز
     */
    findPairBySymbol(symbol) {
        return this.targetPairs.find(pair => pair.symbol === symbol);
    }

    /**
     * استعادة الجلسة المحفوظة
     */
    async restoreSession(session) {
        try {
            console.log('🔄 Attempting to restore saved session...');

            // إطلاق المتصفح مع بيانات المستخدم المحفوظة
            this.browser = await puppeteer.launch({
                headless: this.options.headless,
                userDataDir: this.options.userDataDir,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu'
                ]
            });

            this.page = await this.browser.newPage();

            // تعيين User Agent
            await this.page.setUserAgent(session.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

            // استعادة الكوكيز
            const cookies = this.sessionManager.getCookiesForBrowser();
            if (cookies.length > 0) {
                await this.page.setCookie(...cookies);
                console.log(`🍪 Restored ${cookies.length} cookies`);
            }

            // الانتقال للمنصة
            await this.page.goto('https://qxbroker.com/en/trade', {
                waitUntil: 'networkidle2',
                timeout: this.options.timeout
            });

            // التحقق من حالة تسجيل الدخول
            const isLoggedIn = await this.checkLoginStatus();

            if (isLoggedIn) {
                console.log('✅ Session restored successfully');

                // إعداد اتصال WebSocket
                await this.setupWebSocket();

                // تحديث بيانات الحساب
                await this.loadAccountData();
                await this.loadInstruments();

                // تحديث نشاط الجلسة
                await this.sessionManager.updateSessionActivity({
                    balance: this.accountData.balance
                });

                this.isConnected = true;
                this.emit('connected');
                return true;
            } else {
                console.log('⚠️ Session expired, will perform fresh login');
                await this.sessionManager.clearAll();
                return false;
            }

        } catch (error) {
            console.error('❌ Failed to restore session:', error);
            await this.sessionManager.clearAll();
            return false;
        }
    }

    /**
     * التحقق من حالة تسجيل الدخول
     */
    async checkLoginStatus() {
        try {
            // البحث عن عناصر تدل على تسجيل الدخول
            const loginIndicators = [
                '.trading-view',
                '.asset-select',
                '.balance-amount',
                '[data-test="balance"]'
            ];

            for (const selector of loginIndicators) {
                try {
                    await this.page.waitForSelector(selector, { timeout: 5000 });
                    return true;
                } catch {
                    continue;
                }
            }

            return false;

        } catch (error) {
            console.error('❌ Error checking login status:', error);
            return false;
        }
    }

    /**
     * حفظ الجلسة الحالية
     */
    async saveCurrentSession() {
        try {
            // جمع الكوكيز من المتصفح
            const cookies = await this.page.cookies();
            await this.sessionManager.saveCookies(cookies);

            // جمع بيانات الجلسة من الصفحة
            const sessionInfo = await this.page.evaluate(() => {
                return {
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    sessionId: window.sessionId || null,
                    userId: window.userId || null,
                    email: window.userEmail || null
                };
            });

            const sessionData = {
                email: sessionInfo.email || 'manual_login',
                userId: this.accountData.userId || sessionInfo.userId,
                sessionId: this.accountData.sessionId || sessionInfo.sessionId,
                accountType: this.accountData.isDemo ? 'demo' : 'real',
                balance: this.accountData.balance,
                userAgent: sessionInfo.userAgent,
                url: sessionInfo.url,
                isValid: true,
                timestamp: new Date().toISOString(),
                metadata: {
                    instrumentsCount: this.instruments.size,
                    profitRatesCount: this.profitRates.size
                }
            };

            await this.sessionManager.saveSession(sessionData);
            console.log('💾 Session and cookies saved successfully');

        } catch (error) {
            console.error('❌ Error saving session:', error);
        }
    }

    /**
     * الحصول على السعر الحالي للأصل
     */
    async getCurrentPrice(assetId) {
        try {
            const price = await this.page.evaluate((assetId) => {
                // البحث عن السعر في DOM
                const priceElement = document.querySelector(`[data-asset-id="${assetId}"] .price`) ||
                                   document.querySelector('.current-price') ||
                                   document.querySelector('.asset-price');

                if (priceElement) {
                    return parseFloat(priceElement.textContent.replace(/[^\d.-]/g, ''));
                }

                return null;
            }, assetId);

            return price || 0;

        } catch (error) {
            console.error('❌ Error getting current price:', error);
            return 0;
        }
    }

    /**
     * الحصول على اسم الأصل
     */
    getAssetName(assetId) {
        const instrument = this.instruments.get(assetId);
        if (instrument) {
            return instrument.name;
        }

        // البحث في قائمة الأزواج المستهدفة
        const targetPair = this.targetPairs.find(pair => pair.id === assetId);
        if (targetPair) {
            return targetPair.name;
        }

        return `Asset_${assetId}`;
    }

    /**
     * الحصول على حالة الموصل
     */
    getStatus() {
        return {
            isConnected: this.isConnected,
            isAuthenticated: this.isAuthenticated,
            accountData: this.accountData,
            instrumentsCount: this.instruments.size,
            profitRatesCount: this.profitRates.size,
            targetPairsCount: this.targetPairs.length,
            lastActivity: new Date().toISOString(),
            sessionManager: this.sessionManager ? this.sessionManager.getSessionStats() : null,
            tradeManager: this.tradeManager ? this.tradeManager.getManagerStats() : null,
            candleManager: this.candleManager ? this.candleManager.getStats() : null
        };
    }

    /**
     * الحصول على إحصائيات الصفقات
     */
    getTradeStats() {
        return this.tradeManager.getManagerStats();
    }

    /**
     * الحصول على الصفقات المفتوحة
     */
    getOpenTrades() {
        return this.tradeManager.getOpenTrades();
    }

    /**
     * الحصول على الصفقات المغلقة
     */
    getClosedTrades(limit = 50) {
        return this.tradeManager.getClosedTrades(limit);
    }

    /**
     * تنظيف الصفقات المنتهية الصلاحية
     */
    async cleanupExpiredTrades() {
        return await this.tradeManager.cleanupExpiredTrades();
    }

    /**
     * الحصول على الشموع الحية لأصل معين
     */
    getLiveCandles(assetId, limit = null) {
        return this.candleManager.getLiveCandles(assetId, limit);
    }

    /**
     * الحصول على الشمعة الحالية
     */
    getCurrentCandle(assetId) {
        return this.candleManager.getCurrentCandle(assetId);
    }

    /**
     * الحصول على الشموع التاريخية
     */
    async getHistoricalCandles(assetId, limit = 100) {
        return await this.candleManager.getHistoricalCandles(assetId, limit);
    }

    /**
     * الحصول على إحصائيات الشموع
     */
    getCandleStats() {
        return this.candleManager.getStats();
    }

    /**
     * إغلاق الشمعة الحالية يدوياً
     */
    async closeCurrentCandle(assetId, finalData = {}) {
        return await this.candleManager.closeCurrentCandle(assetId, finalData);
    }

    /**
     * إرسال رسالة WebSocket
     */
    sendWebSocketMessage(event, data) {
        if (this.wsConnection && this.wsConnection.readyState === WebSocket.OPEN) {
            const message = `42${JSON.stringify([event, data])}`;
            this.wsConnection.send(message);
        }
    }

    /**
     * إغلاق الاتصال
     */
    async disconnect() {
        try {
            console.log('🔌 Disconnecting from Quotex...');

            if (this.wsConnection) {
                this.wsConnection.close();
                this.wsConnection = null;
            }

            if (this.page) {
                await this.page.close();
                this.page = null;
            }

            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }

            this.isConnected = false;
            this.isAuthenticated = false;

            // حفظ الجلسة قبل الإغلاق (اختياري)
            if (this.sessionManager && this.sessionManager.getCurrentSession()) {
                await this.sessionManager.updateSessionActivity({
                    metadata: { disconnectedAt: new Date().toISOString() }
                });
            }

            // حفظ وتنظيف بيانات الشموع
            if (this.candleManager) {
                await this.candleManager.cleanup();
            }

            // تنظيف الصفقات المنتهية الصلاحية
            if (this.tradeManager) {
                await this.tradeManager.cleanupExpiredTrades();
            }

            console.log('✅ Disconnected successfully');
            this.emit('disconnected');

        } catch (error) {
            console.error('❌ Error during disconnect:', error);
        }
    }
}

module.exports = QuotexConnector;
