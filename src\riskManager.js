/**
 * مدير المخاطر المتقدم
 * Advanced Risk Manager
 */

const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');

class RiskManager extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            dataDir: options.dataDir || './data/risk',
            configFile: options.configFile || 'risk_config.json',
            logFile: options.logFile || 'risk_log.json',
            ...options
        };

        // إعدادات إدارة المخاطر
        this.riskSettings = {
            // حدود التداول اليومية
            dailyLimits: {
                maxTrades: 50,              // الحد الأقصى للصفقات اليومية
                maxLossAmount: 500,         // الحد الأقصى للخسارة اليومية ($)
                maxLossPercentage: 10,      // الحد الأقصى للخسارة كنسبة من الرصيد (%)
                maxConsecutiveLosses: 5,    // الحد الأقصى للخسائر المتتالية
                cooldownPeriod: 3600000     // فترة التهدئة بعد الوصول للحد (1 ساعة)
            },
            
            // حدود الصفقة الواحدة
            tradeLimits: {
                minAmount: 1,               // الحد الأدنى لمبلغ الصفقة
                maxAmount: 100,             // الحد الأقصى لمبلغ الصفقة
                maxAmountPercentage: 5,     // الحد الأقصى كنسبة من الرصيد (%)
                maxOpenTrades: 10           // الحد الأقصى للصفقات المفتوحة
            },
            
            // إعدادات الثقة والجودة
            confidenceThresholds: {
                minAIConfidence: 0.7,       // الحد الأدنى لثقة الذكاء الاصطناعي
                minTechnicalScore: 0.6,     // الحد الأدنى للنتيجة الفنية
                minLayerAgreement: 0.5      // الحد الأدنى لتوافق الطبقات
            },
            
            // إعدادات التداول الآلي
            autoTrading: {
                enabled: false,
                pauseOnLoss: true,          // إيقاف مؤقت عند الخسارة
                pauseOnLowBalance: true,    // إيقاف عند انخفاض الرصيد
                emergencyStop: true         // إيقاف طوارئ
            }
        };

        // إحصائيات المخاطر الحالية
        this.currentRisk = {
            dailyTrades: 0,
            dailyLoss: 0,
            consecutiveLosses: 0,
            openTrades: 0,
            lastTradeTime: null,
            lastLossTime: null,
            riskLevel: 'low',              // low, medium, high, critical
            isBlocked: false,
            blockReason: null,
            blockUntil: null
        };

        // سجل المخاطر
        this.riskLog = [];
        
        this.isInitialized = false;
    }

    /**
     * تهيئة مدير المخاطر
     */
    async initialize() {
        try {
            console.log('🛡️ Initializing Risk Manager...');

            // إنشاء مجلد البيانات
            await fs.mkdir(this.options.dataDir, { recursive: true });

            // تحميل الإعدادات المحفوظة
            await this.loadSettings();

            // تحميل سجل المخاطر
            await this.loadRiskLog();

            // إعادة تعيين الإحصائيات اليومية إذا كان يوم جديد
            this.checkDailyReset();

            this.isInitialized = true;
            console.log('✅ Risk Manager initialized successfully');
            
            this.emit('initialized');
            return true;

        } catch (error) {
            console.error('❌ Risk Manager initialization failed:', error);
            throw error;
        }
    }

    /**
     * تحميل الإعدادات المحفوظة
     */
    async loadSettings() {
        try {
            const configPath = path.join(this.options.dataDir, this.options.configFile);
            const data = await fs.readFile(configPath, 'utf8');
            const savedSettings = JSON.parse(data);
            
            // دمج الإعدادات المحفوظة مع الافتراضية
            this.riskSettings = { ...this.riskSettings, ...savedSettings };
            console.log('📋 Risk settings loaded from file');

        } catch (error) {
            console.log('⚠️ No saved risk settings found, using defaults');
            await this.saveSettings();
        }
    }

    /**
     * حفظ الإعدادات
     */
    async saveSettings() {
        try {
            const configPath = path.join(this.options.dataDir, this.options.configFile);
            await fs.writeFile(configPath, JSON.stringify(this.riskSettings, null, 2));
            console.log('💾 Risk settings saved');

        } catch (error) {
            console.error('❌ Error saving risk settings:', error);
        }
    }

    /**
     * تحميل سجل المخاطر
     */
    async loadRiskLog() {
        try {
            const logPath = path.join(this.options.dataDir, this.options.logFile);
            const data = await fs.readFile(logPath, 'utf8');
            this.riskLog = JSON.parse(data);
            
            // الاحتفاظ بآخر 1000 مدخل فقط
            if (this.riskLog.length > 1000) {
                this.riskLog = this.riskLog.slice(-1000);
                await this.saveRiskLog();
            }

        } catch (error) {
            console.log('⚠️ No risk log found, starting fresh');
            this.riskLog = [];
        }
    }

    /**
     * حفظ سجل المخاطر
     */
    async saveRiskLog() {
        try {
            const logPath = path.join(this.options.dataDir, this.options.logFile);
            await fs.writeFile(logPath, JSON.stringify(this.riskLog, null, 2));

        } catch (error) {
            console.error('❌ Error saving risk log:', error);
        }
    }

    /**
     * فحص إعادة تعيين يومية
     */
    checkDailyReset() {
        const today = new Date().toISOString().split('T')[0];
        const lastTradeDate = this.currentRisk.lastTradeTime 
            ? new Date(this.currentRisk.lastTradeTime).toISOString().split('T')[0]
            : null;

        if (lastTradeDate && lastTradeDate !== today) {
            this.resetDailyStats();
        }
    }

    /**
     * إعادة تعيين الإحصائيات اليومية
     */
    resetDailyStats() {
        this.currentRisk.dailyTrades = 0;
        this.currentRisk.dailyLoss = 0;
        this.currentRisk.consecutiveLosses = 0;
        this.currentRisk.riskLevel = 'low';
        
        // إلغاء الحظر إذا كان بسبب حدود يومية
        if (this.currentRisk.isBlocked && 
            (this.currentRisk.blockReason === 'daily_limit' || 
             this.currentRisk.blockReason === 'daily_loss')) {
            this.unblockTrading();
        }

        this.addRiskLogEntry('daily_reset', 'Daily risk statistics reset');
        this.emit('dailyReset');
        console.log('📊 Daily risk statistics reset');
    }

    /**
     * تقييم مخاطر الصفقة قبل التنفيذ
     */
    async evaluateTradeRisk(tradeData) {
        try {
            const { asset, amount, direction, confidence, analysis } = tradeData;
            
            // فحص الحظر
            if (this.currentRisk.isBlocked) {
                return {
                    approved: false,
                    reason: this.currentRisk.blockReason,
                    riskLevel: 'blocked',
                    blockUntil: this.currentRisk.blockUntil
                };
            }

            const risks = [];
            let riskScore = 0;

            // فحص حدود التداول اليومية
            if (this.currentRisk.dailyTrades >= this.riskSettings.dailyLimits.maxTrades) {
                risks.push('Daily trade limit exceeded');
                riskScore += 100;
            }

            // فحص حدود الخسارة اليومية
            if (this.currentRisk.dailyLoss >= this.riskSettings.dailyLimits.maxLossAmount) {
                risks.push('Daily loss limit exceeded');
                riskScore += 100;
            }

            // فحص الخسائر المتتالية
            if (this.currentRisk.consecutiveLosses >= this.riskSettings.dailyLimits.maxConsecutiveLosses) {
                risks.push('Consecutive losses limit exceeded');
                riskScore += 80;
            }

            // فحص حدود مبلغ الصفقة
            if (amount < this.riskSettings.tradeLimits.minAmount) {
                risks.push('Trade amount below minimum');
                riskScore += 20;
            }

            if (amount > this.riskSettings.tradeLimits.maxAmount) {
                risks.push('Trade amount above maximum');
                riskScore += 50;
            }

            // فحص عدد الصفقات المفتوحة
            if (this.currentRisk.openTrades >= this.riskSettings.tradeLimits.maxOpenTrades) {
                risks.push('Too many open trades');
                riskScore += 60;
            }

            // فحص مستوى الثقة
            if (confidence < this.riskSettings.confidenceThresholds.minAIConfidence) {
                risks.push('AI confidence below threshold');
                riskScore += 30;
            }

            // تحديد مستوى المخاطر
            let riskLevel;
            if (riskScore >= 100) {
                riskLevel = 'critical';
            } else if (riskScore >= 60) {
                riskLevel = 'high';
            } else if (riskScore >= 30) {
                riskLevel = 'medium';
            } else {
                riskLevel = 'low';
            }

            const approved = riskScore < 100;

            // تسجيل التقييم
            this.addRiskLogEntry('trade_evaluation', {
                asset: asset,
                amount: amount,
                riskScore: riskScore,
                riskLevel: riskLevel,
                risks: risks,
                approved: approved
            });

            return {
                approved: approved,
                riskScore: riskScore,
                riskLevel: riskLevel,
                risks: risks,
                recommendation: this.generateRiskRecommendation(riskScore, risks)
            };

        } catch (error) {
            console.error('❌ Error evaluating trade risk:', error);
            return {
                approved: false,
                reason: 'Risk evaluation error',
                riskLevel: 'critical'
            };
        }
    }

    /**
     * توليد توصية المخاطر
     */
    generateRiskRecommendation(riskScore, risks) {
        if (riskScore >= 100) {
            return 'STOP TRADING - Critical risk level reached';
        } else if (riskScore >= 60) {
            return 'REDUCE POSITION SIZE - High risk detected';
        } else if (riskScore >= 30) {
            return 'PROCEED WITH CAUTION - Medium risk level';
        } else {
            return 'SAFE TO TRADE - Low risk level';
        }
    }

    /**
     * تسجيل تنفيذ صفقة
     */
    recordTradeExecution(tradeData) {
        try {
            this.currentRisk.dailyTrades++;
            this.currentRisk.openTrades++;
            this.currentRisk.lastTradeTime = new Date().toISOString();

            // تحديث مستوى المخاطر
            this.updateRiskLevel();

            this.addRiskLogEntry('trade_executed', {
                asset: tradeData.asset,
                amount: tradeData.amount,
                direction: tradeData.direction,
                dailyTrades: this.currentRisk.dailyTrades,
                openTrades: this.currentRisk.openTrades
            });

            this.emit('tradeExecuted', this.currentRisk);

        } catch (error) {
            console.error('❌ Error recording trade execution:', error);
        }
    }

    /**
     * تسجيل نتيجة صفقة
     */
    recordTradeResult(tradeResult) {
        try {
            const { result, pnl, tradeId } = tradeResult;

            this.currentRisk.openTrades = Math.max(0, this.currentRisk.openTrades - 1);

            if (result === 'loss') {
                this.currentRisk.dailyLoss += Math.abs(pnl || 0);
                this.currentRisk.consecutiveLosses++;
                this.currentRisk.lastLossTime = new Date().toISOString();
            } else if (result === 'win') {
                this.currentRisk.consecutiveLosses = 0; // إعادة تعيين الخسائر المتتالية
            }

            // فحص الحدود وتطبيق الحظر إذا لزم الأمر
            this.checkLimitsAndBlock();

            // تحديث مستوى المخاطر
            this.updateRiskLevel();

            this.addRiskLogEntry('trade_result', {
                tradeId: tradeId,
                result: result,
                pnl: pnl,
                dailyLoss: this.currentRisk.dailyLoss,
                consecutiveLosses: this.currentRisk.consecutiveLosses,
                openTrades: this.currentRisk.openTrades
            });

            this.emit('tradeResult', this.currentRisk);

        } catch (error) {
            console.error('❌ Error recording trade result:', error);
        }
    }

    /**
     * فحص الحدود وتطبيق الحظر
     */
    checkLimitsAndBlock() {
        // فحص حد الخسارة اليومية
        if (this.currentRisk.dailyLoss >= this.riskSettings.dailyLimits.maxLossAmount) {
            this.blockTrading('daily_loss', 'Daily loss limit exceeded');
            return;
        }

        // فحص حد الصفقات اليومية
        if (this.currentRisk.dailyTrades >= this.riskSettings.dailyLimits.maxTrades) {
            this.blockTrading('daily_limit', 'Daily trade limit exceeded');
            return;
        }

        // فحص الخسائر المتتالية
        if (this.currentRisk.consecutiveLosses >= this.riskSettings.dailyLimits.maxConsecutiveLosses) {
            this.blockTrading('consecutive_losses', 'Too many consecutive losses', this.riskSettings.dailyLimits.cooldownPeriod);
            return;
        }
    }

    /**
     * حظر التداول
     */
    blockTrading(reason, message, duration = null) {
        this.currentRisk.isBlocked = true;
        this.currentRisk.blockReason = reason;

        if (duration) {
            this.currentRisk.blockUntil = new Date(Date.now() + duration).toISOString();
        } else {
            this.currentRisk.blockUntil = null; // حظر حتى إعادة تعيين يدوية
        }

        this.addRiskLogEntry('trading_blocked', {
            reason: reason,
            message: message,
            blockUntil: this.currentRisk.blockUntil
        });

        console.log(`🚫 Trading blocked: ${message}`);
        this.emit('tradingBlocked', {
            reason: reason,
            message: message,
            blockUntil: this.currentRisk.blockUntil
        });

        // إيقاف التداول الآلي إذا كان مفعلاً
        if (this.riskSettings.autoTrading.enabled) {
            this.emit('pauseAutoTrading', { reason: reason });
        }
    }

    /**
     * إلغاء حظر التداول
     */
    unblockTrading() {
        if (this.currentRisk.isBlocked) {
            const previousReason = this.currentRisk.blockReason;

            this.currentRisk.isBlocked = false;
            this.currentRisk.blockReason = null;
            this.currentRisk.blockUntil = null;

            this.addRiskLogEntry('trading_unblocked', {
                previousReason: previousReason
            });

            console.log('✅ Trading unblocked');
            this.emit('tradingUnblocked', { previousReason: previousReason });
        }
    }

    /**
     * فحص انتهاء فترة الحظر
     */
    checkBlockExpiry() {
        if (this.currentRisk.isBlocked && this.currentRisk.blockUntil) {
            const now = new Date();
            const blockUntil = new Date(this.currentRisk.blockUntil);

            if (now >= blockUntil) {
                this.unblockTrading();
            }
        }
    }

    /**
     * تحديث مستوى المخاطر
     */
    updateRiskLevel() {
        let riskScore = 0;

        // نسبة الصفقات اليومية
        const dailyTradeRatio = this.currentRisk.dailyTrades / this.riskSettings.dailyLimits.maxTrades;
        riskScore += dailyTradeRatio * 30;

        // نسبة الخسارة اليومية
        const dailyLossRatio = this.currentRisk.dailyLoss / this.riskSettings.dailyLimits.maxLossAmount;
        riskScore += dailyLossRatio * 40;

        // الخسائر المتتالية
        const consecutiveLossRatio = this.currentRisk.consecutiveLosses / this.riskSettings.dailyLimits.maxConsecutiveLosses;
        riskScore += consecutiveLossRatio * 30;

        // تحديد مستوى المخاطر
        if (riskScore >= 80) {
            this.currentRisk.riskLevel = 'critical';
        } else if (riskScore >= 60) {
            this.currentRisk.riskLevel = 'high';
        } else if (riskScore >= 30) {
            this.currentRisk.riskLevel = 'medium';
        } else {
            this.currentRisk.riskLevel = 'low';
        }

        this.emit('riskLevelUpdated', this.currentRisk.riskLevel);
    }

    /**
     * إضافة مدخل لسجل المخاطر
     */
    addRiskLogEntry(type, data) {
        const entry = {
            timestamp: new Date().toISOString(),
            type: type,
            data: data,
            riskLevel: this.currentRisk.riskLevel
        };

        this.riskLog.push(entry);

        // الاحتفاظ بآخر 1000 مدخل فقط
        if (this.riskLog.length > 1000) {
            this.riskLog.shift();
        }

        // حفظ السجل كل 10 مدخلات
        if (this.riskLog.length % 10 === 0) {
            this.saveRiskLog().catch(console.error);
        }
    }

    /**
     * تحديث إعدادات المخاطر
     */
    async updateSettings(newSettings) {
        try {
            this.riskSettings = { ...this.riskSettings, ...newSettings };
            await this.saveSettings();

            this.addRiskLogEntry('settings_updated', newSettings);
            this.emit('settingsUpdated', this.riskSettings);

            console.log('⚙️ Risk settings updated');
            return true;

        } catch (error) {
            console.error('❌ Error updating risk settings:', error);
            return false;
        }
    }

    /**
     * الحصول على تقرير المخاطر الحالي
     */
    getCurrentRiskReport() {
        return {
            currentRisk: { ...this.currentRisk },
            settings: { ...this.riskSettings },
            recentLog: this.riskLog.slice(-20), // آخر 20 مدخل
            recommendations: this.generateRecommendations()
        };
    }

    /**
     * توليد التوصيات
     */
    generateRecommendations() {
        const recommendations = [];

        if (this.currentRisk.riskLevel === 'critical') {
            recommendations.push('Consider stopping trading for today');
            recommendations.push('Review and adjust risk parameters');
        } else if (this.currentRisk.riskLevel === 'high') {
            recommendations.push('Reduce position sizes');
            recommendations.push('Increase confidence thresholds');
        } else if (this.currentRisk.consecutiveLosses >= 3) {
            recommendations.push('Take a break and review strategy');
            recommendations.push('Consider reducing trade frequency');
        }

        if (this.currentRisk.dailyTrades > this.riskSettings.dailyLimits.maxTrades * 0.8) {
            recommendations.push('Approaching daily trade limit');
        }

        if (this.currentRisk.dailyLoss > this.riskSettings.dailyLimits.maxLossAmount * 0.8) {
            recommendations.push('Approaching daily loss limit');
        }

        return recommendations;
    }

    /**
     * بدء المراقبة التلقائية
     */
    startMonitoring() {
        // فحص انتهاء فترة الحظر كل دقيقة
        this.monitoringInterval = setInterval(() => {
            this.checkBlockExpiry();
            this.checkDailyReset();
        }, 60000);

        console.log('🔍 Risk monitoring started');
        this.emit('monitoringStarted');
    }

    /**
     * إيقاف المراقبة التلقائية
     */
    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }

        console.log('🛑 Risk monitoring stopped');
        this.emit('monitoringStopped');
    }
}

module.exports = RiskManager;
