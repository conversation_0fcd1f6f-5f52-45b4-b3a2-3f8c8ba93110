# 🧠 وثيقة أوامر WebSocket - منصة Quotex

توثيق شامل لجميع الأوامر التي يمكن إرسالها أو استقبالها عبر WebSocket في منصة Quotex، بناءً على تحليل رسائل الجلسة الفعلية.

---

## ✅ 1. عرض قائمة الأزواج (Pairs)

**الحدث:** `instruments/list`

```js
socket.emit("instruments/list", {});
```

**الاستجابة:** قائمة بالأصول تشمل:
- `symbol`, `name`, `type`, `status`, `payout`, `change24`

---

## ✅ 2. عرض الرصيد (Balance)

**الحدث:** `balance/list`

```js
socket.emit("balance/list", {});
```

**الاستجابة:** قائمة بأرصدة:
- `amount`, `currency`, `isDemo`

---

## ✅ 3. جلب البيانات التاريخية للشموع

**الحدث:** `chart_notification/get`

```js
socket.emit("chart_notification/get", {
  asset: "EURUSD_otc",
  interval: "M1",
  offset: 0
});
```

**الاستجابة:** قائمة بالشموع:
- `open`, `close`, `high`, `low`, `volume`, `time`

---

## ✅ 4. بث الأسعار اللحظي

**الحدث:** تلقائي من `quotes/stream` بعد:

```js
socket.emit("instruments/follow", { asset: "EURUSD_otc" });
```

**الاستجابة:** أسعار لحظية مستمرة.

---

## ✅ 5. الصفقات المفتوحة

**الحدث:** `orders/opened/list`

```js
socket.emit("orders/opened/list", {});
```

---

## ✅ 6. سجل الصفقات المغلقة

**الحدث:** `orders/closed/list`

```js
socket.emit("orders/closed/list", {});
```

---

## ✅ 7. ضبط مدة الصفقة

داخل أمر `orders/open`:

```js
time: 60 // بالثواني
```

---

## ✅ 8. ضبط المبلغ المستثمر

```js
amount: 5.0
```

---

## ✅ 9. فتح صفقة

**الحدث:** `orders/open`

```js
socket.emit("orders/open", {
  asset: "EURUSD_otc",
  amount: 1.0,
  time: 60,
  action: "call", // أو "put"
  isDemo: 1,
  optionType: 100,
  requestId: 123456
});
```

---

## ✅ 10. قائمة المؤشرات

**الحدث:** `indicator/list`

```js
socket.emit("indicator/list", {});
```

---

## ✅ 11. معلومات الحساب

بعد إرسال `authorization`:

```js
socket.emit("authorization", {
  session: "YOUR_SESSION_ID",
  isDemo: 1,
  tournamentId: 0
});
```

**الاستجابة:** تحتوي على `uid`, `email`, `balance`, `type`

---

## ✅ 12. الأطر الزمنية المتوفرة

غير مباشرة، تظهر من خلال `chart_notification/get` أو `indicator/list`

- الشائع: `M1`, `M5`, `M15`, `M30`, `H1`, `H4`, `D1`

---

## ✅ 13. نسبة الربح لكل زوج

تظهر ضمن `instruments/list` أو `instruments/update`

```json
"payout": 0.82
```

---

## ✅ 14. التغير خلال 24 ساعة

```json
"change24": -0.0015
```

---

## 🧩 أوامر إضافية محتملة

| الحدث | الوظيفة |
|-------|---------|
| `tick` | بث نبضات السوق |
| `depth/follow` | متابعة عمق السوق |
| `settings/store` | تخصيص إعدادات المنصة |
| `pending/list` | الصفقات المعلقة |

---

