<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام التداول الذكي المتقدم - Quotex Smart Trading</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #fff;
        }
        
        .dashboard-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        
        .status-connected { background-color: #28a745; }
        .status-disconnected { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        
        .metric-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #00d4ff;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
        }
        
        .btn-trading {
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-trading:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }
        
        .btn-call {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        
        .btn-put {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
        }
        
        .signal-card {
            background: rgba(40, 167, 69, 0.2);
            border-left: 4px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }
        
        .signal-card.put {
            background: rgba(220, 53, 69, 0.2);
            border-left-color: #dc3545;
        }
        
        .log-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-timestamp {
            color: #6c757d;
            margin-left: 10px;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        
        .sidebar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            height: calc(100vh - 100px);
            overflow-y: auto;
        }
        
        .instrument-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .instrument-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(-5px);
        }
        
        .instrument-item.active {
            background: rgba(0, 212, 255, 0.3);
            border-left: 4px solid #00d4ff;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #00d4ff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .alert-custom {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            color: #fff;
        }
        
        .form-control-custom {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #fff;
        }
        
        .form-control-custom::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }
        
        .form-control-custom:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: #00d4ff;
            box-shadow: 0 0 0 0.2rem rgba(0, 212, 255, 0.25);
            color: #fff;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>
                نظام التداول الذكي
            </a>
            <div class="d-flex align-items-center">
                <span class="me-3">
                    <i class="fas fa-wifi"></i>
                    <span id="connectionStatus">غير متصل</span>
                    <span id="connectionIndicator" class="status-indicator status-disconnected"></span>
                </span>
                <span class="me-3">
                    <i class="fas fa-users"></i>
                    <span id="clientCount">0</span> عميل
                </span>
                <button class="btn btn-outline-light btn-sm" onclick="toggleAutoTrading()">
                    <i class="fas fa-robot"></i>
                    <span id="autoTradingStatus">تفعيل التداول الآلي</span>
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="sidebar">
                    <h5 class="mb-3">
                        <i class="fas fa-list me-2"></i>
                        الأدوات المالية
                    </h5>
                    <div id="instrumentsList">
                        <!-- سيتم ملؤها ديناميكياً -->
                    </div>
                    
                    <hr class="my-4">
                    
                    <h5 class="mb-3">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات سريعة
                    </h5>
                    
                    <div class="mb-3">
                        <label class="form-label">مبلغ التداول</label>
                        <input type="number" class="form-control form-control-custom" id="tradeAmount" value="10" min="1">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">مدة الصفقة (ثانية)</label>
                        <select class="form-control form-control-custom" id="tradeDuration">
                            <option value="60">1 دقيقة</option>
                            <option value="300">5 دقائق</option>
                            <option value="900">15 دقيقة</option>
                            <option value="1800">30 دقيقة</option>
                            <option value="3600">1 ساعة</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">مستوى الثقة الأدنى</label>
                        <input type="range" class="form-range" id="confidenceLevel" min="0.5" max="1" step="0.05" value="0.7">
                        <small class="text-muted">
                            <span id="confidenceValue">70%</span>
                        </small>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9">
                <!-- Status Cards -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="metric-card">
                            <div class="metric-value" id="totalTrades">0</div>
                            <div class="metric-label">إجمالي الصفقات</div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-card">
                            <div class="metric-value" id="winRate">0%</div>
                            <div class="metric-label">نسبة النجاح</div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-card">
                            <div class="metric-value" id="dailyPnL">$0</div>
                            <div class="metric-label">الربح/الخسارة اليومية</div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-card">
                            <div class="metric-value" id="accountBalance">$0</div>
                            <div class="metric-label">رصيد الحساب</div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-card">
                            <div class="metric-value" id="openTrades">0</div>
                            <div class="metric-label">الصفقات المفتوحة</div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="metric-card">
                            <div class="metric-value" id="aiAccuracy">0%</div>
                            <div class="metric-label">دقة الذكاء الاصطناعي</div>
                        </div>
                    </div>
                </div>

                <!-- Trading Pairs Table -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="dashboard-card">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5>
                                    <i class="fas fa-coins me-2"></i>
                                    أزواج العملات (70 زوج)
                                </h5>
                                <div>
                                    <button class="btn btn-outline-light btn-sm" onclick="refreshPairs()">
                                        <i class="fas fa-sync-alt"></i> تحديث
                                    </button>
                                    <button class="btn btn-outline-light btn-sm" onclick="sortPairsByProfit()">
                                        <i class="fas fa-sort-amount-down"></i> ترتيب حسب الربح
                                    </button>
                                </div>
                            </div>
                            <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                                <table class="table table-dark table-striped">
                                    <thead class="sticky-top">
                                        <tr>
                                            <th>الزوج</th>
                                            <th>الحالة</th>
                                            <th>نسبة الربح</th>
                                            <th>السعر الحالي</th>
                                            <th>التغيير 24س</th>
                                            <th>إشارة الذكاء الاصطناعي</th>
                                            <th>الثقة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="pairsTableBody">
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chart and Trading Panel -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="dashboard-card">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5>
                                    <i class="fas fa-chart-candlestick me-2"></i>
                                    الرسم البياني المباشر
                                </h5>
                                <div>
                                    <span id="selectedInstrument">EUR/USD</span>
                                    <span class="badge bg-success ms-2" id="currentPrice">1.0000</span>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="priceChart"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <!-- Trading Panel -->
                        <div class="dashboard-card">
                            <h5 class="mb-3">
                                <i class="fas fa-exchange-alt me-2"></i>
                                لوحة التداول
                            </h5>
                            
                            <div class="d-grid gap-2 mb-3">
                                <button class="btn btn-call btn-trading" onclick="executeTrade('call')">
                                    <i class="fas fa-arrow-up me-2"></i>
                                    شراء (Call)
                                </button>
                                <button class="btn btn-put btn-trading" onclick="executeTrade('put')">
                                    <i class="fas fa-arrow-down me-2"></i>
                                    بيع (Put)
                                </button>
                            </div>
                            
                            <div class="alert alert-custom" id="tradeStatus">
                                <i class="fas fa-info-circle me-2"></i>
                                جاهز للتداول
                            </div>
                        </div>

                        <!-- Signals Panel -->
                        <div class="dashboard-card">
                            <h5 class="mb-3">
                                <i class="fas fa-signal me-2"></i>
                                الإشارات الحية
                            </h5>
                            <div id="signalsContainer">
                                <div class="text-center text-muted">
                                    <i class="fas fa-search me-2"></i>
                                    البحث عن إشارات...
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Open and Closed Trades -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="dashboard-card">
                            <h5 class="mb-3">
                                <i class="fas fa-clock me-2"></i>
                                الصفقات المفتوحة
                            </h5>
                            <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                <table class="table table-dark table-sm">
                                    <thead>
                                        <tr>
                                            <th>الزوج</th>
                                            <th>النوع</th>
                                            <th>المبلغ</th>
                                            <th>الوقت المتبقي</th>
                                            <th>الربح المتوقع</th>
                                        </tr>
                                    </thead>
                                    <tbody id="openTradesTable">
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="dashboard-card">
                            <h5 class="mb-3">
                                <i class="fas fa-check-circle me-2"></i>
                                الصفقات المغلقة (آخر 10)
                            </h5>
                            <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                <table class="table table-dark table-sm">
                                    <thead>
                                        <tr>
                                            <th>الزوج</th>
                                            <th>النوع</th>
                                            <th>المبلغ</th>
                                            <th>النتيجة</th>
                                            <th>الربح/الخسارة</th>
                                            <th>الوقت</th>
                                        </tr>
                                    </thead>
                                    <tbody id="closedTradesTable">
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Auto Trading Control Panel -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="dashboard-card">
                            <h5 class="mb-3">
                                <i class="fas fa-robot me-2"></i>
                                لوحة التحكم في التداول الآلي
                            </h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">تفعيل التداول الآلي</label>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="autoTradingSwitch">
                                            <label class="form-check-label" for="autoTradingSwitch">
                                                <span id="autoTradingLabel">معطل</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">مبلغ الصفقة التلقائية</label>
                                        <input type="number" class="form-control form-control-custom" id="autoTradeAmount" value="10" min="1" max="1000">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">الحد الأقصى للصفقات اليومية</label>
                                        <input type="number" class="form-control form-control-custom" id="maxDailyTrades" value="20" min="1" max="100">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">الحد الأقصى للخسارة اليومية</label>
                                        <input type="number" class="form-control form-control-custom" id="maxDailyLoss" value="100" min="10" max="1000">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">الحد الأدنى لثقة الذكاء الاصطناعي</label>
                                        <input type="range" class="form-range" id="aiConfidenceThreshold" min="0.5" max="0.95" step="0.05" value="0.8">
                                        <small class="text-muted">
                                            <span id="aiConfidenceValue">80%</span>
                                        </small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">استراتيجيات مفعلة</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableTechnical" checked>
                                            <label class="form-check-label" for="enableTechnical">التحليل الفني</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="enableAI" checked>
                                            <label class="form-check-label" for="enableAI">الذكاء الاصطناعي</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">إعدادات المخاطر</label>
                                        <button class="btn btn-outline-warning btn-sm d-block mb-2" onclick="resetDailyStats()">
                                            <i class="fas fa-redo"></i> إعادة تعيين الإحصائيات اليومية
                                        </button>
                                        <button class="btn btn-outline-info btn-sm d-block" onclick="exportTradingData()">
                                            <i class="fas fa-download"></i> تصدير بيانات التداول
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activity Log -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="dashboard-card">
                            <h5 class="mb-3">
                                <i class="fas fa-history me-2"></i>
                                سجل النشاط المباشر
                            </h5>
                            <div class="log-container" id="activityLog">
                                <!-- سيتم ملؤه ديناميكياً -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/advanced-app.js"></script>
</body>
</html>
