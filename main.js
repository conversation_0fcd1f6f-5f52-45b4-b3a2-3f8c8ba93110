/**
 * التطبيق الرئيسي لنظام التداول الذكي المتقدم
 * Main Application for Advanced Smart Trading System
 */

const QuotexConnector = require('./src/quotexConnector');
const HybridStrategy = require('./src/hybridStrategy');
const DataStorage = require('./src/dataStorage');
const SmartTrading = require('./src/smartTrading');
const LiveDataManager = require('./src/liveDataManager');
const WebInterface = require('./src/webInterface');
const PerformanceOptimizer = require('./src/performanceOptimizer');
const RiskManager = require('./src/riskManager');

class TradingSystem {
    constructor() {
        this.quotexConnector = null;
        this.hybridStrategy = null;
        this.dataStorage = null;
        this.smartTrading = null;
        this.liveDataManager = null;
        this.webInterface = null;
        this.performanceOptimizer = null;
        this.riskManager = null;
        this.isRunning = false;
        
        // إعدادات النظام
        this.config = {
            // إعدادات Quotex
            quotex: {
                headless: false, // دائماً مرئي للتسجيل اليدوي
                userDataDir: process.env.QUOTEX_USER_DATA_DIR || './user_data'
            },

            // إعدادات تخزين البيانات
            dataStorage: {
                dataDir: './data',
                historicalDir: './data/historical',
                liveDir: './data/live',
                analysisDir: './data/analysis',
                maxFileSize: 50 * 1024 * 1024, // 50MB
                compressionEnabled: true,
                backupEnabled: true
            },

            // إعدادات الاستراتيجية الهجينة
            hybridStrategy: {
                technical: {
                    minimum_signals: 2,
                    rsi_period: 5,
                    ema_periods: [5, 10, 21]
                },
                quantitative: {
                    zscore_threshold: 2.0,
                    probability_threshold: 0.7
                },
                behavioral: {
                    pattern_strength_min: 'medium'
                },
                ai: {
                    confidence_threshold: 0.8
                },
                decision: {
                    minimum_confidence: 0.8
                }
            },
            
            // إعدادات التداول الذكي
            smartTrading: {
                autoTrade: false,
                minConfidence: 0.7,
                maxTradesPerDay: 10,
                tradingHours: { start: 8, end: 18 },
                enabledStrategies: ['rsi', 'macd', 'bollinger', 'candlestick'],
                riskSettings: {
                    maxRiskPerTrade: 0.02,
                    maxDailyLoss: 0.05,
                    maxConsecutiveLosses: 3,
                    minWinRate: 0.6,
                    maxDrawdown: 0.10
                }
            },
            
            // إعدادات مدير البيانات المباشرة
            liveDataManager: {
                maxCandlesPerInstrument: 1000,
                updateInterval: 1000,
                enableCompression: true,
                enableCaching: true
            },
            
            // إعدادات الواجهة الويب
            webInterface: {
                port: process.env.PORT || 3000,
                host: process.env.HOST || 'localhost',
                enableAuth: false
            },

            // إعدادات محسن الأداء
            performanceOptimizer: {
                memoryThreshold: 100 * 1024 * 1024, // 100MB
                cpuThreshold: 80,
                latencyThreshold: 1000,
                cleanupInterval: 60000,
                optimizationInterval: 300000
            }
        };
    }

    /**
     * تهيئة النظام
     */
    async initialize() {
        try {
            console.log('🚀 Initializing Advanced Hybrid Trading System...');

            // تهيئة موصل Quotex
            console.log('📡 Initializing Quotex Connector...');
            this.quotexConnector = new QuotexConnector({
                headless: this.config.quotex.headless,
                userDataDir: this.config.quotex.userDataDir
            });

            // تهيئة نظام تخزين البيانات
            console.log('💾 Initializing Data Storage...');
            this.dataStorage = new DataStorage(this.config.dataStorage);

            // تهيئة الاستراتيجية الهجينة
            console.log('🧠 Initializing Hybrid Strategy...');
            this.hybridStrategy = new HybridStrategy(this.config.hybridStrategy);

            // تهيئة مدير البيانات المباشرة
            console.log('📊 Initializing Live Data Manager...');
            this.liveDataManager = new LiveDataManager(this.quotexConnector, this.config.liveDataManager);

            // تهيئة نظام التداول الذكي
            console.log('🎯 Initializing Smart Trading System...');
            this.smartTrading = new SmartTrading(this.quotexConnector, this.config.smartTrading);

            // تهيئة محسن الأداء
            console.log('⚡ Initializing Performance Optimizer...');
            this.performanceOptimizer = new PerformanceOptimizer(this.config.performanceOptimizer);

            // تهيئة مدير المخاطر
            console.log('🛡️ Initializing Risk Manager...');
            this.riskManager = new RiskManager(this.config.riskManager || {});
            await this.riskManager.initialize();

            // تهيئة الواجهة الويب
            console.log('🌐 Initializing Web Interface...');
            this.webInterface = new WebInterface(
                this.quotexConnector,
                this.smartTrading,
                this.liveDataManager,
                this.config.webInterface
            );

            // إعداد معالجات الأحداث
            this.setupEventHandlers();

            console.log('✅ System initialization completed successfully');
            return true;

        } catch (error) {
            console.error('❌ System initialization failed:', error);
            throw error;
        }
    }

    /**
     * إعداد معالجات الأحداث
     */
    setupEventHandlers() {
        // معالجات Quotex Connector
        this.quotexConnector.on('connected', () => {
            console.log('🔐 Quotex platform connected successfully');
            this.startTradingOperations();
        });

        this.quotexConnector.on('authenticated', () => {
            console.log('✅ Quotex authentication successful');
        });

        this.quotexConnector.on('disconnected', () => {
            console.log('🔌 Quotex platform disconnected');
            this.handleQuotexDisconnection();
        });

        this.quotexConnector.on('error', (error) => {
            console.error('❌ Quotex error:', error.message);
            this.handleQuotexError(error);
        });

        this.quotexConnector.on('priceUpdate', (data) => {
            this.handlePriceUpdate(data);
        });

        this.quotexConnector.on('candleUpdate', (data) => {
            this.handleCandleUpdate(data);
        });

        this.quotexConnector.on('tradeResult', (data) => {
            this.handleTradeResult(data);
        });

        // معالجات الاستراتيجية الهجينة
        this.hybridStrategy.on('analysisCompleted', (analysis) => {
            console.log(`🧠 Hybrid analysis completed for asset ${analysis.assetId}`);
            this.webInterface.broadcast('hybridAnalysis', analysis);
        });

        this.hybridStrategy.on('analysisError', (error) => {
            console.error('❌ Hybrid strategy analysis error:', error);
        });

        // معالجات تخزين البيانات
        this.dataStorage.on('historicalDataSaved', (data) => {
            console.log(`💾 Historical data saved: ${data.filename}`);
        });

        this.dataStorage.on('liveDataSaved', (data) => {
            console.log(`📊 Live data saved for asset ${data.assetId}`);
        });

        this.dataStorage.on('analysisSaved', (data) => {
            console.log(`🔍 Analysis saved: ${data.filename}`);
        });

        this.dataStorage.on('error', (error) => {
            console.error('❌ Data storage error:', error);
        });

        // معالجات محسن الأداء
        this.performanceOptimizer.on('optimizationCompleted', (data) => {
            console.log(`⚡ Performance optimization completed: ${data.type}`);
        });

        this.performanceOptimizer.on('cleanupCompleted', () => {
            console.log('🧹 Performance cleanup completed');
        });

        // معالجات مدير البيانات المباشرة
        this.liveDataManager.on('subscribed', (data) => {
            console.log(`📊 Subscribed to instrument ${data.instrumentId}`);
        });

        this.liveDataManager.on('liveDataUpdate', (data) => {
            this.handleLiveDataUpdate(data);
        });

        this.liveDataManager.on('candleCompleted', (data) => {
            this.handleCandleCompleted(data);
        });

        this.liveDataManager.on('error', (error) => {
            console.error('❌ Live Data Manager error:', error);
        });

        // معالجة إشارات النظام
        process.on('SIGINT', () => {
            console.log('\n🛑 Received SIGINT, shutting down gracefully...');
            this.shutdown();
        });

        process.on('SIGTERM', () => {
            console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
            this.shutdown();
        });

        process.on('uncaughtException', (error) => {
            console.error('❌ Uncaught Exception:', error);
            this.shutdown();
        });

        process.on('unhandledRejection', (reason, promise) => {
            console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
        });
    }

    /**
     * بدء النظام
     */
    async start() {
        try {
            if (this.isRunning) {
                console.log('⚠️ System is already running');
                return;
            }

            console.log('🚀 Starting Advanced Hybrid Trading System...');

            // الاتصال بمنصة Quotex مع التسجيل اليدوي
            console.log('📡 Connecting to Quotex platform...');
            console.log('👤 Browser will open for manual login...');
            await this.quotexConnector.connect();

            // بدء محسن الأداء
            console.log('⚡ Starting Performance Optimizer...');
            this.performanceOptimizer.startMonitoring();

            // بدء الواجهة الويب
            console.log('🌐 Starting Web Interface...');
            await this.webInterface.start();

            // تفعيل المراقبة المباشرة للصفقات
            if (this.quotexConnector.tradeManager) {
                this.quotexConnector.tradeManager.startLiveMonitoring();
                console.log('🔍 Live trade monitoring enabled');
            }

            // تفعيل مراقبة المخاطر
            if (this.riskManager) {
                this.riskManager.startMonitoring();
                console.log('🛡️ Risk monitoring enabled');
            }

            this.isRunning = true;
            console.log('✅ System started successfully!');
            console.log(`🌐 Web interface available at: http://${this.config.webInterface.host}:${this.config.webInterface.port}`);

            return true;

        } catch (error) {
            console.error('❌ Failed to start system:', error);
            throw error;
        }
    }

    /**
     * بدء عمليات التداول
     */
    async startTradingOperations() {
        try {
            console.log('📈 Starting hybrid trading operations...');

            // تحميل قائمة الأدوات المالية
            const instruments = this.quotexConnector.getInstruments();
            console.log(`📋 Found ${instruments.length} available instruments`);

            // حفظ قائمة الأدوات
            await this.dataStorage.saveInstruments(instruments);

            // تحميل نسب الأرباح
            const profitRates = this.quotexConnector.getProfitRates();
            await this.dataStorage.saveProfitRates(profitRates);

            // الاشتراك في الأدوات المالية الرئيسية
            const mainInstruments = instruments.slice(0, 10).map(i => i.id); // أول 10 أدوات

            for (const instrumentId of mainInstruments) {
                // جلب البيانات التاريخية
                const historicalData = await this.quotexConnector.getHistoricalData(instrumentId, 60, 100);
                if (historicalData && historicalData.length > 0) {
                    // تحليل البيانات التاريخية باستخدام الاستراتيجية الهجينة
                    const analysis = await this.hybridStrategy.analyzeMarket(instrumentId, historicalData);

                    // حفظ البيانات والتحليل
                    await this.dataStorage.saveHistoricalData(instrumentId, 60, historicalData, analysis);
                    await this.dataStorage.saveAnalysis(instrumentId, 60, analysis);
                }

                // الاشتراك في البيانات المباشرة
                await this.liveDataManager.subscribeToInstrument(instrumentId, 60);

                // تأخير بين الاشتراكات
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            console.log('✅ Hybrid trading operations started successfully');

        } catch (error) {
            console.error('❌ Failed to start trading operations:', error);
        }
    }

    /**
     * معالجة تحديث البيانات المباشرة
     */
    handleLiveDataUpdate(data) {
        // تحليل السوق إذا كان التداول الآلي مفعل
        if (this.config.smartTrading.autoTrade) {
            this.analyzeAndTrade(data.instrumentId);
        }
    }

    /**
     * معالجة اكتمال الشمعة
     */
    async handleCandleCompleted(data) {
        console.log(`🕯️ Candle completed for instrument ${data.instrumentId}`);
        
        // تحليل السوق عند اكتمال كل شمعة
        if (this.config.smartTrading.autoTrade) {
            await this.analyzeAndTrade(data.instrumentId);
        }
    }

    /**
     * تحليل السوق والتداول باستخدام الاستراتيجية الهجينة
     */
    async analyzeAndTrade(instrumentId) {
        try {
            const combinedData = this.liveDataManager.getCombinedData(instrumentId);

            if (!combinedData || combinedData.candles.length < 30) {
                console.log(`⚠️ Insufficient data for instrument ${instrumentId}`);
                return;
            }

            console.log(`🔍 Analyzing instrument ${instrumentId} with hybrid strategy...`);

            // تحليل باستخدام الاستراتيجية الهجينة
            const decision = await this.hybridStrategy.analyzeMarket(instrumentId, combinedData.candles);

            // حفظ التحليل
            await this.dataStorage.saveAnalysis(instrumentId, 60, decision);

            if (decision.signal && decision.confidence >= this.config.hybridStrategy.decision.minimum_confidence) {
                console.log(`🎯 Strong hybrid signal detected for instrument ${instrumentId}:`);
                console.log(`   Direction: ${decision.direction}`);
                console.log(`   Confidence: ${(decision.confidence * 100).toFixed(1)}%`);
                console.log(`   Reasons: ${decision.reasons.join(', ')}`);

                // الحصول على رصيد الحساب
                const accountData = this.quotexConnector.getAccountBalance();

                // تحديد مبلغ الصفقة بناء على الثقة
                let tradeAmount = 10; // مبلغ افتراضي
                if (decision.confidence >= 0.9) {
                    tradeAmount = 20; // ثقة عالية جداً
                } else if (decision.confidence >= 0.85) {
                    tradeAmount = 15; // ثقة عالية
                }

                // تنفيذ الصفقة
                const tradeResult = await this.quotexConnector.placeTrade(
                    instrumentId,
                    tradeAmount,
                    decision.direction,
                    300 // 5 دقائق
                );

                if (tradeResult.success) {
                    console.log('✅ Hybrid strategy trade executed successfully:', tradeResult);

                    // تسجيل الصفقة في إحصائيات الأداء
                    this.performanceOptimizer.recordOperation(true, Date.now() - decision.timestamp.getTime());

                    // إشعار الواجهة الويب
                    this.webInterface.broadcast('tradeExecuted', {
                        instrumentId: instrumentId,
                        decision: decision,
                        result: tradeResult,
                        timestamp: new Date()
                    });

                } else {
                    console.log('⚠️ Hybrid strategy trade not executed:', tradeResult.reason);
                    this.performanceOptimizer.recordOperation(false);
                }
            } else {
                console.log(`📊 Instrument ${instrumentId}: No signal or confidence too low (${(decision.confidence * 100).toFixed(1)}%)`);
            }

        } catch (error) {
            console.error('❌ Error in hybrid analyze and trade:', error);
            this.performanceOptimizer.recordOperation(false);
        }
    }

    /**
     * معالجة تحديث الأسعار
     */
    handlePriceUpdate(data) {
        // إرسال تحديث الأسعار للواجهة الويب
        this.webInterface.broadcast('priceUpdate', data);
    }

    /**
     * معالجة تحديث الشموع
     */
    async handleCandleUpdate(data) {
        try {
            // حفظ الشمعة الجديدة
            await this.dataStorage.saveLiveData(data.assetId, data);

            // تحليل الشمعة الجديدة إذا كان التداول الآلي مفعل
            if (this.config.smartTrading.autoTrade) {
                await this.analyzeAndTrade(data.assetId);
            }

            // إرسال تحديث للواجهة الويب
            this.webInterface.broadcast('candleUpdate', data);

        } catch (error) {
            console.error('❌ Error handling candle update:', error);
        }
    }

    /**
     * معالجة نتائج الصفقات
     */
    async handleTradeResult(data) {
        try {
            console.log(`📊 Trade result received:`, data);

            // تحديث إحصائيات الأداء
            const isWin = data.result === 'win';
            this.performanceOptimizer.recordOperation(isWin);

            // إرسال النتيجة للواجهة الويب
            this.webInterface.broadcast('tradeResult', data);

        } catch (error) {
            console.error('❌ Error handling trade result:', error);
        }
    }

    /**
     * معالجة انقطاع Quotex
     */
    handleQuotexDisconnection() {
        console.log('🔄 Attempting to reconnect to Quotex...');
        // سيتم إعادة الاتصال تلقائياً بواسطة QuotexConnector
    }

    /**
     * معالجة خطأ Quotex
     */
    handleQuotexError(error) {
        console.error('❌ Quotex Error:', error.message);

        // إشعار الواجهة الويب
        if (this.webInterface) {
            this.webInterface.broadcast('quotexError', {
                error: error.message,
                timestamp: new Date()
            });
        }
    }

    /**
     * إيقاف النظام
     */
    async shutdown() {
        try {
            console.log('🛑 Shutting down hybrid trading system...');
            this.isRunning = false;

            // إيقاف محسن الأداء
            if (this.performanceOptimizer) {
                this.performanceOptimizer.stopMonitoring();
                console.log('✅ Performance optimizer stopped');
            }

            // إيقاف الواجهة الويب
            if (this.webInterface) {
                await this.webInterface.stop();
                console.log('✅ Web interface stopped');
            }

            // إيقاف مدير البيانات المباشرة
            if (this.liveDataManager) {
                this.liveDataManager.stop();
                console.log('✅ Live data manager stopped');
            }

            // تنظيف البيانات القديمة
            if (this.dataStorage) {
                await this.dataStorage.cleanupOldFiles(30); // حذف الملفات الأقدم من 30 يوم
                console.log('✅ Data storage cleanup completed');
            }

            // إغلاق اتصال Quotex
            if (this.quotexConnector) {
                await this.quotexConnector.disconnect();
                console.log('✅ Quotex connection closed');
            }

            console.log('✅ Hybrid trading system shutdown completed');
            process.exit(0);

        } catch (error) {
            console.error('❌ Error during shutdown:', error);
            process.exit(1);
        }
    }

    /**
     * الحصول على حالة النظام
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            quotexConnector: {
                isConnected: this.quotexConnector?.isConnected || false,
                isAuthenticated: this.quotexConnector?.isAuthenticated || false,
                accountData: this.quotexConnector?.getAccountBalance() || null
            },
            hybridStrategy: {
                performanceStats: this.hybridStrategy?.getPerformanceStats() || null,
                lastAnalysis: this.hybridStrategy?.getFullAnalysis() || null
            },
            dataStorage: {
                stats: this.dataStorage?.getStorageStats() || null
            },
            performanceOptimizer: {
                isMonitoring: this.performanceOptimizer?.isMonitoring || false,
                report: this.performanceOptimizer?.getPerformanceReport() || null
            },
            liveDataManager: this.liveDataManager?.getStatus() || null,
            smartTrading: this.smartTrading?.getPerformanceReport() || null,
            webInterface: this.webInterface?.getServerInfo() || null,
            timestamp: new Date()
        };
    }
}

// تشغيل النظام
async function main() {
    const system = new TradingSystem();
    
    try {
        await system.initialize();
        await system.start();
        
        // عرض حالة النظام كل دقيقة
        setInterval(() => {
            const status = system.getStatus();
            console.log('📊 System Status:', JSON.stringify(status, null, 2));
        }, 60000);
        
    } catch (error) {
        console.error('❌ Failed to start trading system:', error);
        process.exit(1);
    }
}

// تشغيل التطبيق إذا تم استدعاؤه مباشرة
if (require.main === module) {
    main().catch(console.error);
}

module.exports = TradingSystem;
