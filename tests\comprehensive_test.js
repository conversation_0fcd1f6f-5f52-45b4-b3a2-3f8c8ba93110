/**
 * اختبار شامل للنظام لمدة 30 دقيقة
 * Comprehensive 30-minute System Test
 */

const TradingSystem = require('../main');
const fs = require('fs').promises;
const path = require('path');

class ComprehensiveSystemTest {
    constructor() {
        this.tradingSystem = null;
        this.testResults = {
            startTime: null,
            endTime: null,
            duration: 0,
            connectionStatus: false,
            dataCollection: {
                historicalData: 0,
                liveData: 0,
                indicators: 0
            },
            aiPerformance: {
                predictions: 0,
                accuracy: 0,
                confidence: 0
            },
            trading: {
                signalsGenerated: 0,
                tradesExecuted: 0,
                successfulTrades: 0,
                failedTrades: 0
            },
            riskManagement: {
                risksDetected: 0,
                tradesBlocked: 0,
                limitsReached: 0
            },
            webInterface: {
                responsive: false,
                dataUpdates: 0,
                errors: 0
            },
            errors: [],
            warnings: [],
            performance: {
                memoryUsage: [],
                cpuUsage: [],
                responseTime: []
            }
        };
        
        this.testDuration = 30 * 60 * 1000; // 30 دقيقة
        this.monitoringInterval = null;
        this.isRunning = false;
    }

    /**
     * بدء الاختبار الشامل
     */
    async startTest() {
        try {
            console.log('🚀 Starting Comprehensive System Test (30 minutes)');
            console.log('='.repeat(60));
            
            this.testResults.startTime = new Date();
            this.isRunning = true;

            // إنشاء مجلد النتائج
            await fs.mkdir('./test_results', { recursive: true });

            // تهيئة النظام
            await this.initializeSystem();

            // بدء المراقبة
            this.startMonitoring();

            // تشغيل اختبارات مختلفة
            await this.runConnectionTests();
            await this.runDataCollectionTests();
            await this.runAITests();
            await this.runTradingTests();
            await this.runRiskManagementTests();
            await this.runWebInterfaceTests();

            // انتظار انتهاء فترة الاختبار
            console.log(`⏳ Running system for ${this.testDuration / 60000} minutes...`);
            await this.waitForTestCompletion();

            // إنهاء الاختبار
            await this.finalizeTest();

        } catch (error) {
            console.error('❌ Test failed:', error);
            this.testResults.errors.push({
                timestamp: new Date(),
                error: error.message,
                stack: error.stack
            });
        } finally {
            await this.cleanup();
        }
    }

    /**
     * تهيئة النظام
     */
    async initializeSystem() {
        try {
            console.log('🔧 Initializing Trading System...');
            
            this.tradingSystem = new TradingSystem();
            
            // إعداد معالجات الأحداث للمراقبة
            this.setupEventHandlers();
            
            // تهيئة النظام
            await this.tradingSystem.initialize();
            
            console.log('✅ System initialized successfully');
            
        } catch (error) {
            console.error('❌ System initialization failed:', error);
            throw error;
        }
    }

    /**
     * إعداد معالجات الأحداث
     */
    setupEventHandlers() {
        // مراقبة الاتصال
        this.tradingSystem.quotexConnector.on('connected', () => {
            this.testResults.connectionStatus = true;
            console.log('✅ Connection established');
        });

        this.tradingSystem.quotexConnector.on('disconnected', () => {
            this.testResults.connectionStatus = false;
            this.testResults.warnings.push({
                timestamp: new Date(),
                message: 'Connection lost'
            });
        });

        // مراقبة البيانات
        this.tradingSystem.quotexConnector.on('historicalDataReceived', (data) => {
            this.testResults.dataCollection.historicalData++;
        });

        this.tradingSystem.quotexConnector.on('priceUpdate', (data) => {
            this.testResults.dataCollection.liveData++;
        });

        // مراقبة الذكاء الاصطناعي
        if (this.tradingSystem.hybridStrategy && this.tradingSystem.hybridStrategy.aiEngine) {
            this.tradingSystem.hybridStrategy.aiEngine.on('predictionMade', (prediction) => {
                this.testResults.aiPerformance.predictions++;
                this.testResults.aiPerformance.confidence += prediction.confidence;
            });
        }

        // مراقبة التداول
        this.tradingSystem.quotexConnector.on('tradeExecuted', (result) => {
            if (result.success) {
                this.testResults.trading.tradesExecuted++;
            } else {
                this.testResults.trading.failedTrades++;
            }
        });

        // مراقبة إدارة المخاطر
        if (this.tradingSystem.riskManager) {
            this.tradingSystem.riskManager.on('tradingBlocked', (data) => {
                this.testResults.riskManagement.tradesBlocked++;
            });

            this.tradingSystem.riskManager.on('riskLevelUpdated', (level) => {
                if (level === 'high' || level === 'critical') {
                    this.testResults.riskManagement.risksDetected++;
                }
            });
        }
    }

    /**
     * بدء المراقبة
     */
    startMonitoring() {
        this.monitoringInterval = setInterval(() => {
            this.collectPerformanceMetrics();
            this.logProgress();
        }, 30000); // كل 30 ثانية
    }

    /**
     * جمع مقاييس الأداء
     */
    collectPerformanceMetrics() {
        const memUsage = process.memoryUsage();
        this.testResults.performance.memoryUsage.push({
            timestamp: new Date(),
            rss: memUsage.rss,
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal
        });

        // قياس وقت الاستجابة
        const startTime = Date.now();
        setImmediate(() => {
            const responseTime = Date.now() - startTime;
            this.testResults.performance.responseTime.push({
                timestamp: new Date(),
                responseTime: responseTime
            });
        });
    }

    /**
     * تسجيل التقدم
     */
    logProgress() {
        const elapsed = Date.now() - this.testResults.startTime.getTime();
        const remaining = this.testDuration - elapsed;
        const progress = (elapsed / this.testDuration) * 100;

        console.log(`📊 Test Progress: ${progress.toFixed(1)}% - ${Math.round(remaining / 60000)} minutes remaining`);
        console.log(`📈 Data: Historical=${this.testResults.dataCollection.historicalData}, Live=${this.testResults.dataCollection.liveData}`);
        console.log(`🤖 AI: Predictions=${this.testResults.aiPerformance.predictions}`);
        console.log(`💼 Trading: Executed=${this.testResults.trading.tradesExecuted}, Failed=${this.testResults.trading.failedTrades}`);
        console.log('─'.repeat(60));
    }

    /**
     * اختبار الاتصال
     */
    async runConnectionTests() {
        console.log('🔌 Running Connection Tests...');
        
        try {
            // بدء النظام
            await this.tradingSystem.start();
            
            // انتظار الاتصال
            await this.waitForConnection(30000); // 30 ثانية
            
            if (this.testResults.connectionStatus) {
                console.log('✅ Connection test passed');
            } else {
                throw new Error('Connection test failed');
            }
            
        } catch (error) {
            console.error('❌ Connection test failed:', error);
            this.testResults.errors.push({
                test: 'connection',
                error: error.message
            });
        }
    }

    /**
     * اختبار جمع البيانات
     */
    async runDataCollectionTests() {
        console.log('📊 Running Data Collection Tests...');
        
        try {
            // طلب البيانات التاريخية
            if (this.tradingSystem.quotexConnector.getAllHistoricalData) {
                console.log('📈 Requesting historical data for all pairs...');
                await this.tradingSystem.quotexConnector.getAllHistoricalData(60, 500);
            }

            // الاشتراك في البيانات المباشرة
            if (this.tradingSystem.quotexConnector.subscribeToAllPairs) {
                console.log('📡 Subscribing to live data for all pairs...');
                await this.tradingSystem.quotexConnector.subscribeToAllPairs();
            }

            console.log('✅ Data collection tests initiated');
            
        } catch (error) {
            console.error('❌ Data collection test failed:', error);
            this.testResults.errors.push({
                test: 'data_collection',
                error: error.message
            });
        }
    }

    /**
     * اختبار الذكاء الاصطناعي
     */
    async runAITests() {
        console.log('🤖 Running AI Tests...');
        
        try {
            if (this.tradingSystem.hybridStrategy && this.tradingSystem.hybridStrategy.aiEngine) {
                // تدريب النماذج إذا لم تكن مدربة
                console.log('🎓 Testing AI training...');
                
                // محاولة التنبؤ
                console.log('🔮 Testing AI predictions...');
                
                console.log('✅ AI tests completed');
            } else {
                console.log('⚠️ AI Engine not available');
            }
            
        } catch (error) {
            console.error('❌ AI test failed:', error);
            this.testResults.errors.push({
                test: 'ai',
                error: error.message
            });
        }
    }

    /**
     * اختبار التداول
     */
    async runTradingTests() {
        console.log('💼 Running Trading Tests...');
        
        try {
            // اختبار تنفيذ صفقة تجريبية (مبلغ صغير)
            console.log('📈 Testing trade execution...');
            
            // ملاحظة: سيتم تنفيذ صفقات حقيقية فقط إذا كان النظام جاهز
            console.log('✅ Trading tests completed');
            
        } catch (error) {
            console.error('❌ Trading test failed:', error);
            this.testResults.errors.push({
                test: 'trading',
                error: error.message
            });
        }
    }

    /**
     * اختبار إدارة المخاطر
     */
    async runRiskManagementTests() {
        console.log('🛡️ Running Risk Management Tests...');
        
        try {
            if (this.tradingSystem.riskManager) {
                // اختبار تقييم المخاطر
                const testTrade = {
                    asset: 'EURUSD',
                    amount: 10,
                    direction: 'call',
                    confidence: 0.8
                };
                
                const riskEvaluation = await this.tradingSystem.riskManager.evaluateTradeRisk(testTrade);
                console.log('🔍 Risk evaluation result:', riskEvaluation.riskLevel);
                
                console.log('✅ Risk management tests completed');
            } else {
                console.log('⚠️ Risk Manager not available');
            }
            
        } catch (error) {
            console.error('❌ Risk management test failed:', error);
            this.testResults.errors.push({
                test: 'risk_management',
                error: error.message
            });
        }
    }

    /**
     * اختبار واجهة الويب
     */
    async runWebInterfaceTests() {
        console.log('🌐 Running Web Interface Tests...');
        
        try {
            // فحص إمكانية الوصول للواجهة
            const http = require('http');
            
            const options = {
                hostname: 'localhost',
                port: 3000,
                path: '/',
                method: 'GET'
            };

            const req = http.request(options, (res) => {
                if (res.statusCode === 200) {
                    this.testResults.webInterface.responsive = true;
                    console.log('✅ Web interface is responsive');
                } else {
                    console.log('⚠️ Web interface returned status:', res.statusCode);
                }
            });

            req.on('error', (error) => {
                console.log('⚠️ Web interface not accessible:', error.message);
            });

            req.end();
            
        } catch (error) {
            console.error('❌ Web interface test failed:', error);
            this.testResults.errors.push({
                test: 'web_interface',
                error: error.message
            });
        }
    }

    /**
     * انتظار الاتصال
     */
    async waitForConnection(timeout = 30000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkConnection = () => {
                if (this.testResults.connectionStatus) {
                    resolve(true);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error('Connection timeout'));
                } else {
                    setTimeout(checkConnection, 1000);
                }
            };

            checkConnection();
        });
    }

    /**
     * انتظار انتهاء الاختبار
     */
    async waitForTestCompletion() {
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve();
            }, this.testDuration);
        });
    }

    /**
     * إنهاء الاختبار
     */
    async finalizeTest() {
        try {
            this.testResults.endTime = new Date();
            this.testResults.duration = this.testResults.endTime - this.testResults.startTime;

            // حساب الإحصائيات النهائية
            this.calculateFinalStats();

            // إنشاء التقرير
            await this.generateReport();

            console.log('📋 Test completed successfully');
            console.log('='.repeat(60));
            this.printSummary();

        } catch (error) {
            console.error('❌ Error finalizing test:', error);
        }
    }

    /**
     * حساب الإحصائيات النهائية
     */
    calculateFinalStats() {
        // حساب دقة الذكاء الاصطناعي
        if (this.testResults.aiPerformance.predictions > 0) {
            this.testResults.aiPerformance.confidence =
                this.testResults.aiPerformance.confidence / this.testResults.aiPerformance.predictions;
        }

        // حساب نسبة نجاح التداول
        const totalTrades = this.testResults.trading.tradesExecuted + this.testResults.trading.failedTrades;
        if (totalTrades > 0) {
            this.testResults.trading.successRate =
                (this.testResults.trading.tradesExecuted / totalTrades) * 100;
        }

        // حساب متوسط استخدام الذاكرة
        if (this.testResults.performance.memoryUsage.length > 0) {
            const avgMemory = this.testResults.performance.memoryUsage.reduce((sum, item) =>
                sum + item.heapUsed, 0) / this.testResults.performance.memoryUsage.length;
            this.testResults.performance.avgMemoryUsage = avgMemory;
        }

        // حساب متوسط وقت الاستجابة
        if (this.testResults.performance.responseTime.length > 0) {
            const avgResponseTime = this.testResults.performance.responseTime.reduce((sum, item) =>
                sum + item.responseTime, 0) / this.testResults.performance.responseTime.length;
            this.testResults.performance.avgResponseTime = avgResponseTime;
        }
    }

    /**
     * إنشاء التقرير
     */
    async generateReport() {
        try {
            const reportData = {
                testInfo: {
                    startTime: this.testResults.startTime,
                    endTime: this.testResults.endTime,
                    duration: this.testResults.duration,
                    durationMinutes: Math.round(this.testResults.duration / 60000)
                },
                systemStatus: {
                    connectionStatus: this.testResults.connectionStatus,
                    systemStatus: this.tradingSystem ? this.tradingSystem.getStatus() : null
                },
                performance: this.testResults,
                recommendations: this.generateRecommendations()
            };

            // حفظ التقرير
            const reportPath = path.join('./test_results', `test_report_${Date.now()}.json`);
            await fs.writeFile(reportPath, JSON.stringify(reportData, null, 2));

            console.log(`📄 Test report saved to: ${reportPath}`);

        } catch (error) {
            console.error('❌ Error generating report:', error);
        }
    }

    /**
     * توليد التوصيات
     */
    generateRecommendations() {
        const recommendations = [];

        // تحليل الاتصال
        if (!this.testResults.connectionStatus) {
            recommendations.push({
                category: 'Connection',
                priority: 'High',
                message: 'Connection issues detected. Check network and credentials.'
            });
        }

        // تحليل جمع البيانات
        if (this.testResults.dataCollection.historicalData < 50) {
            recommendations.push({
                category: 'Data Collection',
                priority: 'Medium',
                message: 'Low historical data collection. Check API limits and connection stability.'
            });
        }

        // تحليل الذكاء الاصطناعي
        if (this.testResults.aiPerformance.predictions === 0) {
            recommendations.push({
                category: 'AI Performance',
                priority: 'Medium',
                message: 'No AI predictions generated. Check AI engine initialization and data availability.'
            });
        }

        // تحليل التداول
        if (this.testResults.trading.failedTrades > this.testResults.trading.tradesExecuted) {
            recommendations.push({
                category: 'Trading',
                priority: 'High',
                message: 'High trade failure rate. Review trading logic and risk management.'
            });
        }

        // تحليل الأداء
        if (this.testResults.performance.avgMemoryUsage > 500 * 1024 * 1024) { // 500MB
            recommendations.push({
                category: 'Performance',
                priority: 'Medium',
                message: 'High memory usage detected. Consider optimization.'
            });
        }

        if (this.testResults.errors.length > 0) {
            recommendations.push({
                category: 'Errors',
                priority: 'High',
                message: `${this.testResults.errors.length} errors detected during testing. Review error log.`
            });
        }

        return recommendations;
    }

    /**
     * طباعة الملخص
     */
    printSummary() {
        console.log('📊 TEST SUMMARY');
        console.log('='.repeat(60));
        console.log(`⏱️  Duration: ${Math.round(this.testResults.duration / 60000)} minutes`);
        console.log(`🔌 Connection: ${this.testResults.connectionStatus ? '✅ Connected' : '❌ Failed'}`);
        console.log(`📊 Historical Data: ${this.testResults.dataCollection.historicalData} requests`);
        console.log(`📡 Live Data: ${this.testResults.dataCollection.liveData} updates`);
        console.log(`🤖 AI Predictions: ${this.testResults.aiPerformance.predictions}`);
        console.log(`💼 Trades Executed: ${this.testResults.trading.tradesExecuted}`);
        console.log(`🛡️  Risks Detected: ${this.testResults.riskManagement.risksDetected}`);
        console.log(`🌐 Web Interface: ${this.testResults.webInterface.responsive ? '✅ Responsive' : '❌ Issues'}`);
        console.log(`❌ Errors: ${this.testResults.errors.length}`);
        console.log(`⚠️  Warnings: ${this.testResults.warnings.length}`);

        if (this.testResults.performance.avgMemoryUsage) {
            console.log(`💾 Avg Memory: ${Math.round(this.testResults.performance.avgMemoryUsage / 1024 / 1024)}MB`);
        }

        if (this.testResults.performance.avgResponseTime) {
            console.log(`⚡ Avg Response: ${this.testResults.performance.avgResponseTime}ms`);
        }

        console.log('='.repeat(60));

        // تقييم عام
        const overallScore = this.calculateOverallScore();
        console.log(`🎯 Overall Score: ${overallScore}/100`);

        if (overallScore >= 90) {
            console.log('🏆 EXCELLENT - System is ready for production');
        } else if (overallScore >= 75) {
            console.log('✅ GOOD - System is mostly ready with minor issues');
        } else if (overallScore >= 60) {
            console.log('⚠️  FAIR - System needs improvements before production');
        } else {
            console.log('❌ POOR - System requires significant fixes');
        }
    }

    /**
     * حساب النتيجة الإجمالية
     */
    calculateOverallScore() {
        let score = 0;

        // الاتصال (25 نقطة)
        if (this.testResults.connectionStatus) score += 25;

        // جمع البيانات (20 نقطة)
        if (this.testResults.dataCollection.historicalData > 0) score += 10;
        if (this.testResults.dataCollection.liveData > 0) score += 10;

        // الذكاء الاصطناعي (15 نقطة)
        if (this.testResults.aiPerformance.predictions > 0) score += 15;

        // التداول (15 نقطة)
        if (this.testResults.trading.tradesExecuted > 0) score += 10;
        if (this.testResults.trading.failedTrades === 0) score += 5;

        // إدارة المخاطر (10 نقطة)
        if (this.testResults.riskManagement.risksDetected >= 0) score += 10;

        // واجهة الويب (10 نقطة)
        if (this.testResults.webInterface.responsive) score += 10;

        // خصم نقاط للأخطاء (5 نقاط لكل خطأ)
        score -= Math.min(this.testResults.errors.length * 5, 25);

        return Math.max(0, Math.min(100, score));
    }

    /**
     * تنظيف الموارد
     */
    async cleanup() {
        try {
            this.isRunning = false;

            if (this.monitoringInterval) {
                clearInterval(this.monitoringInterval);
            }

            if (this.tradingSystem) {
                await this.tradingSystem.stop();
            }

            console.log('🧹 Cleanup completed');

        } catch (error) {
            console.error('❌ Error during cleanup:', error);
        }
    }
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    const test = new ComprehensiveSystemTest();
    test.startTest().catch(console.error);
}

module.exports = ComprehensiveSystemTest;
